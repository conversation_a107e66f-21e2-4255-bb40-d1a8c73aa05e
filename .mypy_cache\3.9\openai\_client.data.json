{".class": "MypyFile", "_fullname": "openai._client", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "APIStatusError": {".class": "SymbolTableNode", "cross_ref": "openai._exceptions.APIStatusError", "kind": "Gdef", "module_public": false}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AsyncAPIClient": {".class": "SymbolTableNode", "cross_ref": "openai._base_client.AsyncAPIClient", "kind": "Gdef", "module_public": false}, "AsyncAudio": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.AsyncAudio", "kind": "Gdef", "module_public": false}, "AsyncBatches": {".class": "SymbolTableNode", "cross_ref": "openai.resources.batches.AsyncBatches", "kind": "Gdef", "module_public": false}, "AsyncBeta": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.AsyncBeta", "kind": "Gdef", "module_public": false}, "AsyncChat": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.AsyncChat", "kind": "Gdef", "module_public": false}, "AsyncClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "openai._client.AsyncClient", "line": 1129, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "openai._client.AsyncOpenAI"}}, "AsyncCompletions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.completions.AsyncCompletions", "kind": "Gdef", "module_public": false}, "AsyncContainers": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.AsyncContainers", "kind": "Gdef", "module_public": false}, "AsyncEmbeddings": {".class": "SymbolTableNode", "cross_ref": "openai.resources.embeddings.AsyncEmbeddings", "kind": "Gdef", "module_public": false}, "AsyncEvals": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.AsyncEvals", "kind": "Gdef", "module_public": false}, "AsyncFiles": {".class": "SymbolTableNode", "cross_ref": "openai.resources.files.AsyncFiles", "kind": "Gdef", "module_public": false}, "AsyncFineTuning": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuning", "kind": "Gdef", "module_public": false}, "AsyncImages": {".class": "SymbolTableNode", "cross_ref": "openai.resources.images.AsyncImages", "kind": "Gdef", "module_public": false}, "AsyncModels": {".class": "SymbolTableNode", "cross_ref": "openai.resources.models.AsyncModels", "kind": "Gdef", "module_public": false}, "AsyncModerations": {".class": "SymbolTableNode", "cross_ref": "openai.resources.moderations.AsyncModerations", "kind": "Gdef", "module_public": false}, "AsyncOpenAI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._base_client.AsyncAPIClient"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._client.AsyncOpenAI", "name": "AsyncOpenAI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._client.AsyncOpenAI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai._client", "mro": ["openai._client.AsyncOpenAI", "openai._base_client.AsyncAPIClient", "openai._base_client.BaseClient", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_key", "organization", "project", "webhook_secret", "base_url", "websocket_base_url", "timeout", "max_retries", "default_headers", "default_query", "http_client", "_strict_response_validation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_key", "organization", "project", "webhook_secret", "base_url", "websocket_base_url", "timeout", "max_retries", "default_headers", "default_query", "http_client", "_strict_response_validation"], "arg_types": ["openai._client.AsyncOpenAI", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["httpx._client.AsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncOpenAI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_status_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "err_msg", "body", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI._make_status_error", "name": "_make_status_error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "err_msg", "body", "response"], "arg_types": ["openai._client.AsyncOpenAI", "builtins.str", "builtins.object", "httpx._models.Response"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_status_error of AsyncOpenAI", "ret_type": "openai._exceptions.APIStatusError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI._make_status_error", "name": "_make_status_error", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "err_msg", "body", "response"], "arg_types": ["openai._client.AsyncOpenAI", "builtins.str", "builtins.object", "httpx._models.Response"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_status_error of AsyncOpenAI", "ret_type": "openai._exceptions.APIStatusError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "api_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.AsyncOpenAI.api_key", "name": "api_key", "setter_type": null, "type": "builtins.str"}}, "audio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.audio", "name": "audio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of AsyncOpenAI", "ret_type": "openai.resources.audio.audio.AsyncAudio", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.audio", "name": "audio", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of AsyncOpenAI", "ret_type": "openai.resources.audio.audio.AsyncAudio", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "auth_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.auth_headers", "name": "auth_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "auth_headers of AsyncOpenAI", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.auth_headers", "name": "auth_headers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "auth_headers of AsyncOpenAI", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.batches", "name": "batches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of AsyncOpenAI", "ret_type": "openai.resources.batches.AsyncBatches", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.batches", "name": "batches", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of AsyncOpenAI", "ret_type": "openai.resources.batches.AsyncBatches", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "beta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.beta", "name": "beta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of AsyncOpenAI", "ret_type": "openai.resources.beta.beta.AsyncBeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.beta", "name": "beta", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of AsyncOpenAI", "ret_type": "openai.resources.beta.beta.AsyncBeta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "chat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.chat", "name": "chat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of AsyncOpenAI", "ret_type": "openai.resources.chat.chat.AsyncChat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.chat", "name": "chat", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of AsyncOpenAI", "ret_type": "openai.resources.chat.chat.AsyncChat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.completions", "name": "completions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of AsyncOpenAI", "ret_type": "openai.resources.completions.AsyncCompletions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.completions", "name": "completions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of AsyncOpenAI", "ret_type": "openai.resources.completions.AsyncCompletions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "containers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.containers", "name": "containers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of AsyncOpenAI", "ret_type": "openai.resources.containers.containers.AsyncContainers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.containers", "name": "containers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of AsyncOpenAI", "ret_type": "openai.resources.containers.containers.AsyncContainers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_key", "organization", "project", "webhook_secret", "websocket_base_url", "base_url", "timeout", "http_client", "max_retries", "default_headers", "set_default_headers", "default_query", "set_default_query", "_extra_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai._client.AsyncOpenAI.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_key", "organization", "project", "webhook_secret", "websocket_base_url", "base_url", "timeout", "http_client", "max_retries", "default_headers", "set_default_headers", "default_query", "set_default_query", "_extra_kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.AsyncOpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.AsyncOpenAI", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["httpx._client.AsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "copy of AsyncOpenAI", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.AsyncOpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.AsyncOpenAI", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.AsyncOpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.AsyncOpenAI", "values": [], "variance": 0}]}}}, "default_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.default_headers", "name": "default_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "default_headers of AsyncOpenAI", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.Omit"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.default_headers", "name": "default_headers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "default_headers of AsyncOpenAI", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.Omit"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.embeddings", "name": "embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of AsyncOpenAI", "ret_type": "openai.resources.embeddings.AsyncEmbeddings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.embeddings", "name": "embeddings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of AsyncOpenAI", "ret_type": "openai.resources.embeddings.AsyncEmbeddings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "evals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.evals", "name": "evals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of AsyncOpenAI", "ret_type": "openai.resources.evals.evals.AsyncEvals", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.evals", "name": "evals", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of AsyncOpenAI", "ret_type": "openai.resources.evals.evals.AsyncEvals", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.files", "name": "files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of AsyncOpenAI", "ret_type": "openai.resources.files.AsyncFiles", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.files", "name": "files", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of AsyncOpenAI", "ret_type": "openai.resources.files.AsyncFiles", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fine_tuning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.fine_tuning", "name": "fine_tuning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of AsyncOpenAI", "ret_type": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuning", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.fine_tuning", "name": "fine_tuning", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of AsyncOpenAI", "ret_type": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuning", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "images": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.images", "name": "images", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of AsyncOpenAI", "ret_type": "openai.resources.images.AsyncImages", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.images", "name": "images", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of AsyncOpenAI", "ret_type": "openai.resources.images.AsyncImages", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.models", "name": "models", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of AsyncOpenAI", "ret_type": "openai.resources.models.AsyncModels", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.models", "name": "models", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of AsyncOpenAI", "ret_type": "openai.resources.models.AsyncModels", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "moderations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.moderations", "name": "moderations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of AsyncOpenAI", "ret_type": "openai.resources.moderations.AsyncModerations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.moderations", "name": "moderations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of AsyncOpenAI", "ret_type": "openai.resources.moderations.AsyncModerations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "organization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.AsyncOpenAI.organization", "name": "organization", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.AsyncOpenAI.project", "name": "project", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "qs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.qs", "name": "qs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "qs of AsyncOpenAI", "ret_type": "openai._qs.Querystring", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.qs", "name": "qs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "qs of AsyncOpenAI", "ret_type": "openai._qs.Querystring", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "responses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.responses", "name": "responses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of AsyncOpenAI", "ret_type": "openai.resources.responses.responses.AsyncResponses", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.responses", "name": "responses", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of AsyncOpenAI", "ret_type": "openai.resources.responses.responses.AsyncResponses", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "uploads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.uploads", "name": "uploads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of AsyncOpenAI", "ret_type": "openai.resources.uploads.uploads.AsyncUploads", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.uploads", "name": "uploads", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of AsyncOpenAI", "ret_type": "openai.resources.uploads.uploads.AsyncUploads", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "vector_stores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.vector_stores", "name": "vector_stores", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of AsyncOpenAI", "ret_type": "openai.resources.vector_stores.vector_stores.AsyncVectorStores", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.vector_stores", "name": "vector_stores", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of AsyncOpenAI", "ret_type": "openai.resources.vector_stores.vector_stores.AsyncVectorStores", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "webhook_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.AsyncOpenAI.webhook_secret", "name": "webhook_secret", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "webhooks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.webhooks", "name": "webhooks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "webhooks of AsyncOpenAI", "ret_type": "openai.resources.webhooks.AsyncWebhooks", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.webhooks", "name": "webhooks", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "webhooks of AsyncOpenAI", "ret_type": "openai.resources.webhooks.AsyncWebhooks", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "websocket_base_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.AsyncOpenAI.websocket_base_url", "name": "websocket_base_url", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "with_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "openai._client.AsyncOpenAI.with_options", "name": "with_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_key", "organization", "project", "webhook_secret", "websocket_base_url", "base_url", "timeout", "http_client", "max_retries", "default_headers", "set_default_headers", "default_query", "set_default_query", "_extra_kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.AsyncOpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.AsyncOpenAI", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["httpx._client.AsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.AsyncOpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.AsyncOpenAI", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.AsyncOpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.AsyncOpenAI", "values": [], "variance": 0}]}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncOpenAI", "ret_type": "openai._client.AsyncOpenAIWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncOpenAI", "ret_type": "openai._client.AsyncOpenAIWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAI.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncOpenAI", "ret_type": "openai._client.AsyncOpenAIWithStreamedResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAI.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncOpenAI", "ret_type": "openai._client.AsyncOpenAIWithStreamedResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.AsyncOpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.AsyncOpenAI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncOpenAIWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._client.AsyncOpenAIWithRawResponse", "name": "AsyncOpenAIWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._client.AsyncOpenAIWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai._client", "mro": ["openai._client.AsyncOpenAIWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "client"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse", "openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncOpenAIWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.AsyncOpenAIWithRawResponse._client", "name": "_client", "setter_type": null, "type": "openai._client.AsyncOpenAI"}}, "audio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.audio", "name": "audio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.audio.audio.AsyncAudioWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.audio", "name": "audio", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.audio.audio.AsyncAudioWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.batches", "name": "batches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.batches.AsyncBatchesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.batches", "name": "batches", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.batches.AsyncBatchesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "beta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.beta", "name": "beta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.beta.beta.AsyncBetaWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.beta", "name": "beta", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.beta.beta.AsyncBetaWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "chat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.chat", "name": "chat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.chat.chat.AsyncChatWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.chat", "name": "chat", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.chat.chat.AsyncChatWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.completions", "name": "completions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.completions.AsyncCompletionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.completions", "name": "completions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.completions.AsyncCompletionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "containers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.containers", "name": "containers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.containers.containers.AsyncContainersWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.containers", "name": "containers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.containers.containers.AsyncContainersWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.embeddings", "name": "embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.embeddings.AsyncEmbeddingsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.embeddings", "name": "embeddings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.embeddings.AsyncEmbeddingsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "evals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.evals", "name": "evals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.evals.evals.AsyncEvalsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.evals", "name": "evals", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.evals.evals.AsyncEvalsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.files", "name": "files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.files.AsyncFilesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.files", "name": "files", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.files.AsyncFilesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fine_tuning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.fine_tuning", "name": "fine_tuning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuningWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.fine_tuning", "name": "fine_tuning", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuningWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "images": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.images", "name": "images", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.images.AsyncImagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.images", "name": "images", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.images.AsyncImagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.models", "name": "models", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.models.AsyncModelsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.models", "name": "models", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.models.AsyncModelsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "moderations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.moderations", "name": "moderations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.moderations.AsyncModerationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.moderations", "name": "moderations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.moderations.AsyncModerationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "responses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.responses", "name": "responses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.responses.responses.AsyncResponsesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.responses", "name": "responses", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.responses.responses.AsyncResponsesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "uploads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.uploads", "name": "uploads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.uploads.uploads.AsyncUploadsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.uploads", "name": "uploads", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.uploads.uploads.AsyncUploadsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "vector_stores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.vector_stores", "name": "vector_stores", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.vector_stores.vector_stores.AsyncVectorStoresWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithRawResponse.vector_stores", "name": "vector_stores", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of AsyncOpenAIWithRawResponse", "ret_type": "openai.resources.vector_stores.vector_stores.AsyncVectorStoresWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.AsyncOpenAIWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.AsyncOpenAIWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncOpenAIWithStreamedResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._client.AsyncOpenAIWithStreamedResponse", "name": "AsyncOpenAIWithStreamedResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai._client", "mro": ["openai._client.AsyncOpenAIWithStreamedResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "client"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse", "openai._client.AsyncOpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncOpenAIWithStreamedResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse._client", "name": "_client", "setter_type": null, "type": "openai._client.AsyncOpenAI"}}, "audio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.audio", "name": "audio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.audio", "name": "audio", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.batches", "name": "batches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.batches.AsyncBatchesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.batches", "name": "batches", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.batches.AsyncBatchesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "beta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.beta", "name": "beta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.beta.beta.AsyncBetaWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.beta", "name": "beta", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.beta.beta.AsyncBetaWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "chat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.chat", "name": "chat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.chat.chat.AsyncChatWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.chat", "name": "chat", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.chat.chat.AsyncChatWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.completions", "name": "completions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.completions.AsyncCompletionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.completions", "name": "completions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.completions.AsyncCompletionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "containers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.containers", "name": "containers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.containers.containers.AsyncContainersWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.containers", "name": "containers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.containers.containers.AsyncContainersWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.embeddings", "name": "embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.embeddings.AsyncEmbeddingsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.embeddings", "name": "embeddings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.embeddings.AsyncEmbeddingsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "evals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.evals", "name": "evals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.evals", "name": "evals", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.files", "name": "files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.files.AsyncFilesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.files", "name": "files", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.files.AsyncFilesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fine_tuning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.fine_tuning", "name": "fine_tuning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuningWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.fine_tuning", "name": "fine_tuning", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuningWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "images": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.images", "name": "images", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.images.AsyncImagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.images", "name": "images", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.images.AsyncImagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.models", "name": "models", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.models.AsyncModelsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.models", "name": "models", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.models.AsyncModelsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "moderations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.moderations", "name": "moderations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.moderations.AsyncModerationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.moderations", "name": "moderations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.moderations.AsyncModerationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "responses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.responses", "name": "responses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.responses.responses.AsyncResponsesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.responses", "name": "responses", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.responses.responses.AsyncResponsesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "uploads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.uploads", "name": "uploads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.uploads.uploads.AsyncUploadsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.uploads", "name": "uploads", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.uploads.uploads.AsyncUploadsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "vector_stores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.vector_stores", "name": "vector_stores", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.vector_stores.vector_stores.AsyncVectorStoresWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.vector_stores", "name": "vector_stores", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.AsyncOpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of AsyncOpenAIWithStreamedResponse", "ret_type": "openai.resources.vector_stores.vector_stores.AsyncVectorStoresWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.AsyncOpenAIWithStreamedResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.AsyncOpenAIWithStreamedResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncResponses": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.responses.AsyncResponses", "kind": "Gdef", "module_public": false}, "AsyncStream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.AsyncStream", "kind": "Gdef", "module_public": false}, "AsyncUploads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.AsyncUploads", "kind": "Gdef", "module_public": false}, "AsyncVectorStores": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.AsyncVectorStores", "kind": "Gdef", "module_public": false}, "AsyncWebhooks": {".class": "SymbolTableNode", "cross_ref": "openai.resources.webhooks.AsyncWebhooks", "kind": "Gdef", "module_public": false}, "Audio": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.Audio", "kind": "Gdef", "module_public": false}, "Batches": {".class": "SymbolTableNode", "cross_ref": "openai.resources.batches.Batches", "kind": "Gdef", "module_public": false}, "Beta": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.Beta", "kind": "Gdef", "module_public": false}, "Chat": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.Chat", "kind": "Gdef", "module_public": false}, "Client": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "openai._client.Client", "line": 1127, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "openai._client.OpenAI"}}, "Completions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.completions.Completions", "kind": "Gdef", "module_public": false}, "Containers": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.Containers", "kind": "Gdef", "module_public": false}, "DEFAULT_MAX_RETRIES": {".class": "SymbolTableNode", "cross_ref": "openai._constants.DEFAULT_MAX_RETRIES", "kind": "Gdef", "module_public": false}, "Embeddings": {".class": "SymbolTableNode", "cross_ref": "openai.resources.embeddings.Embeddings", "kind": "Gdef", "module_public": false}, "Evals": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.Evals", "kind": "Gdef", "module_public": false}, "Files": {".class": "SymbolTableNode", "cross_ref": "openai.resources.files.Files", "kind": "Gdef", "module_public": false}, "FineTuning": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.FineTuning", "kind": "Gdef", "module_public": false}, "Images": {".class": "SymbolTableNode", "cross_ref": "openai.resources.images.Images", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "Models": {".class": "SymbolTableNode", "cross_ref": "openai.resources.models.Models", "kind": "Gdef", "module_public": false}, "Moderations": {".class": "SymbolTableNode", "cross_ref": "openai.resources.moderations.Moderations", "kind": "Gdef", "module_public": false}, "NOT_GIVEN": {".class": "SymbolTableNode", "cross_ref": "openai._types.NOT_GIVEN", "kind": "Gdef", "module_public": false}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef", "module_public": false}, "Omit": {".class": "SymbolTableNode", "cross_ref": "openai._types.Omit", "kind": "Gdef", "module_public": false}, "OpenAI": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._base_client.SyncAPIClient"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._client.OpenAI", "name": "OpenAI", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._client.OpenAI", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai._client", "mro": ["openai._client.OpenAI", "openai._base_client.SyncAPIClient", "openai._base_client.BaseClient", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_key", "organization", "project", "webhook_secret", "base_url", "websocket_base_url", "timeout", "max_retries", "default_headers", "default_query", "http_client", "_strict_response_validation"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._client.OpenAI.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_key", "organization", "project", "webhook_secret", "base_url", "websocket_base_url", "timeout", "max_retries", "default_headers", "default_query", "http_client", "_strict_response_validation"], "arg_types": ["openai._client.OpenAI", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, "builtins.int", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["httpx._client.Client", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.bool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of OpenAI", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_make_status_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "err_msg", "body", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI._make_status_error", "name": "_make_status_error", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "err_msg", "body", "response"], "arg_types": ["openai._client.OpenAI", "builtins.str", "builtins.object", "httpx._models.Response"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_status_error of OpenAI", "ret_type": "openai._exceptions.APIStatusError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI._make_status_error", "name": "_make_status_error", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "err_msg", "body", "response"], "arg_types": ["openai._client.OpenAI", "builtins.str", "builtins.object", "httpx._models.Response"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_status_error of OpenAI", "ret_type": "openai._exceptions.APIStatusError", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "api_key": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.OpenAI.api_key", "name": "api_key", "setter_type": null, "type": "builtins.str"}}, "audio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.audio", "name": "audio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of OpenAI", "ret_type": "openai.resources.audio.audio.Audio", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.audio", "name": "audio", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of OpenAI", "ret_type": "openai.resources.audio.audio.Audio", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "auth_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.auth_headers", "name": "auth_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "auth_headers of OpenAI", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.auth_headers", "name": "auth_headers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "auth_headers of OpenAI", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.batches", "name": "batches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of OpenAI", "ret_type": "openai.resources.batches.Batches", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.batches", "name": "batches", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of OpenAI", "ret_type": "openai.resources.batches.Batches", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "beta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.beta", "name": "beta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of OpenAI", "ret_type": "openai.resources.beta.beta.Beta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.beta", "name": "beta", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of OpenAI", "ret_type": "openai.resources.beta.beta.Beta", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "chat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.chat", "name": "chat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of OpenAI", "ret_type": "openai.resources.chat.chat.Chat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.chat", "name": "chat", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of OpenAI", "ret_type": "openai.resources.chat.chat.Chat", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.completions", "name": "completions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of OpenAI", "ret_type": "openai.resources.completions.Completions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.completions", "name": "completions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of OpenAI", "ret_type": "openai.resources.completions.Completions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "containers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.containers", "name": "containers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of OpenAI", "ret_type": "openai.resources.containers.containers.Containers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.containers", "name": "containers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of OpenAI", "ret_type": "openai.resources.containers.containers.Containers", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "copy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_key", "organization", "project", "webhook_secret", "websocket_base_url", "base_url", "timeout", "http_client", "max_retries", "default_headers", "set_default_headers", "default_query", "set_default_query", "_extra_kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai._client.OpenAI.copy", "name": "copy", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_key", "organization", "project", "webhook_secret", "websocket_base_url", "base_url", "timeout", "http_client", "max_retries", "default_headers", "set_default_headers", "default_query", "set_default_query", "_extra_kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.OpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.OpenAI", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["httpx._client.Client", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "copy of OpenAI", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.OpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.OpenAI", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.OpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.OpenAI", "values": [], "variance": 0}]}}}, "default_headers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.default_headers", "name": "default_headers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "default_headers of OpenAI", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.Omit"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.default_headers", "name": "default_headers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "default_headers of OpenAI", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.Omit"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.embeddings", "name": "embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of OpenAI", "ret_type": "openai.resources.embeddings.Embeddings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.embeddings", "name": "embeddings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of OpenAI", "ret_type": "openai.resources.embeddings.Embeddings", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "evals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.evals", "name": "evals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of OpenAI", "ret_type": "openai.resources.evals.evals.Evals", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.evals", "name": "evals", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of OpenAI", "ret_type": "openai.resources.evals.evals.Evals", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.files", "name": "files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of OpenAI", "ret_type": "openai.resources.files.Files", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.files", "name": "files", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of OpenAI", "ret_type": "openai.resources.files.Files", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fine_tuning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.fine_tuning", "name": "fine_tuning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of OpenAI", "ret_type": "openai.resources.fine_tuning.fine_tuning.FineTuning", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.fine_tuning", "name": "fine_tuning", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of OpenAI", "ret_type": "openai.resources.fine_tuning.fine_tuning.FineTuning", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "images": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.images", "name": "images", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of OpenAI", "ret_type": "openai.resources.images.Images", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.images", "name": "images", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of OpenAI", "ret_type": "openai.resources.images.Images", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.models", "name": "models", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of OpenAI", "ret_type": "openai.resources.models.Models", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.models", "name": "models", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of OpenAI", "ret_type": "openai.resources.models.Models", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "moderations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.moderations", "name": "moderations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of OpenAI", "ret_type": "openai.resources.moderations.Moderations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.moderations", "name": "moderations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of OpenAI", "ret_type": "openai.resources.moderations.Moderations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "organization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.OpenAI.organization", "name": "organization", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "project": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.OpenAI.project", "name": "project", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "qs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.qs", "name": "qs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "qs of OpenAI", "ret_type": "openai._qs.Querystring", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.qs", "name": "qs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "qs of OpenAI", "ret_type": "openai._qs.Querystring", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "responses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.responses", "name": "responses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of OpenAI", "ret_type": "openai.resources.responses.responses.Responses", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.responses", "name": "responses", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of OpenAI", "ret_type": "openai.resources.responses.responses.Responses", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "uploads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.uploads", "name": "uploads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of OpenAI", "ret_type": "openai.resources.uploads.uploads.Uploads", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.uploads", "name": "uploads", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of OpenAI", "ret_type": "openai.resources.uploads.uploads.Uploads", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "vector_stores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.vector_stores", "name": "vector_stores", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of OpenAI", "ret_type": "openai.resources.vector_stores.vector_stores.VectorStores", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.vector_stores", "name": "vector_stores", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of OpenAI", "ret_type": "openai.resources.vector_stores.vector_stores.VectorStores", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "webhook_secret": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.OpenAI.webhook_secret", "name": "webhook_secret", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "webhooks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.webhooks", "name": "webhooks", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "webhooks of OpenAI", "ret_type": "openai.resources.webhooks.Webhooks", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.webhooks", "name": "webhooks", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "webhooks of OpenAI", "ret_type": "openai.resources.webhooks.Webhooks", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "websocket_base_url": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.OpenAI.websocket_base_url", "name": "websocket_base_url", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "with_options": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "openai._client.OpenAI.with_options", "name": "with_options", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "api_key", "organization", "project", "webhook_secret", "websocket_base_url", "base_url", "timeout", "http_client", "max_retries", "default_headers", "set_default_headers", "default_query", "set_default_query", "_extra_kwargs"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.OpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.OpenAI", "values": [], "variance": 0}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "httpx._urls.URL", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["httpx._client.Client", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Mapping"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Mapping"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.OpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.OpenAI", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.OpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.OpenAI", "values": [], "variance": 0}]}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of OpenAI", "ret_type": "openai._client.OpenAIWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of OpenAI", "ret_type": "openai._client.OpenAIWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAI.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of OpenAI", "ret_type": "openai._client.OpenAIWithStreamedResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAI.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of OpenAI", "ret_type": "openai._client.OpenAIWithStreamedResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.OpenAI.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.OpenAI", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OpenAIError": {".class": "SymbolTableNode", "cross_ref": "openai._exceptions.OpenAIError", "kind": "Gdef", "module_public": false}, "OpenAIWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._client.OpenAIWithRawResponse", "name": "OpenAIWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._client.OpenAIWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai._client", "mro": ["openai._client.OpenAIWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "client"], "arg_types": ["openai._client.OpenAIWithRawResponse", "openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of OpenAIWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.OpenAIWithRawResponse._client", "name": "_client", "setter_type": null, "type": "openai._client.OpenAI"}}, "audio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.audio", "name": "audio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of OpenAIWithRawResponse", "ret_type": "openai.resources.audio.audio.AudioWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.audio", "name": "audio", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of OpenAIWithRawResponse", "ret_type": "openai.resources.audio.audio.AudioWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.batches", "name": "batches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of OpenAIWithRawResponse", "ret_type": "openai.resources.batches.BatchesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.batches", "name": "batches", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of OpenAIWithRawResponse", "ret_type": "openai.resources.batches.BatchesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "beta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.beta", "name": "beta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of OpenAIWithRawResponse", "ret_type": "openai.resources.beta.beta.BetaWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.beta", "name": "beta", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of OpenAIWithRawResponse", "ret_type": "openai.resources.beta.beta.BetaWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "chat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.chat", "name": "chat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of OpenAIWithRawResponse", "ret_type": "openai.resources.chat.chat.ChatWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.chat", "name": "chat", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of OpenAIWithRawResponse", "ret_type": "openai.resources.chat.chat.ChatWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.completions", "name": "completions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of OpenAIWithRawResponse", "ret_type": "openai.resources.completions.CompletionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.completions", "name": "completions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of OpenAIWithRawResponse", "ret_type": "openai.resources.completions.CompletionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "containers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.containers", "name": "containers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of OpenAIWithRawResponse", "ret_type": "openai.resources.containers.containers.ContainersWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.containers", "name": "containers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of OpenAIWithRawResponse", "ret_type": "openai.resources.containers.containers.ContainersWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.embeddings", "name": "embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of OpenAIWithRawResponse", "ret_type": "openai.resources.embeddings.EmbeddingsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.embeddings", "name": "embeddings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of OpenAIWithRawResponse", "ret_type": "openai.resources.embeddings.EmbeddingsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "evals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.evals", "name": "evals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of OpenAIWithRawResponse", "ret_type": "openai.resources.evals.evals.EvalsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.evals", "name": "evals", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of OpenAIWithRawResponse", "ret_type": "openai.resources.evals.evals.EvalsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.files", "name": "files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of OpenAIWithRawResponse", "ret_type": "openai.resources.files.FilesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.files", "name": "files", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of OpenAIWithRawResponse", "ret_type": "openai.resources.files.FilesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fine_tuning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.fine_tuning", "name": "fine_tuning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of OpenAIWithRawResponse", "ret_type": "openai.resources.fine_tuning.fine_tuning.FineTuningWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.fine_tuning", "name": "fine_tuning", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of OpenAIWithRawResponse", "ret_type": "openai.resources.fine_tuning.fine_tuning.FineTuningWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "images": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.images", "name": "images", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of OpenAIWithRawResponse", "ret_type": "openai.resources.images.ImagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.images", "name": "images", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of OpenAIWithRawResponse", "ret_type": "openai.resources.images.ImagesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.models", "name": "models", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of OpenAIWithRawResponse", "ret_type": "openai.resources.models.ModelsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.models", "name": "models", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of OpenAIWithRawResponse", "ret_type": "openai.resources.models.ModelsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "moderations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.moderations", "name": "moderations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of OpenAIWithRawResponse", "ret_type": "openai.resources.moderations.ModerationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.moderations", "name": "moderations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of OpenAIWithRawResponse", "ret_type": "openai.resources.moderations.ModerationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "responses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.responses", "name": "responses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of OpenAIWithRawResponse", "ret_type": "openai.resources.responses.responses.ResponsesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.responses", "name": "responses", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of OpenAIWithRawResponse", "ret_type": "openai.resources.responses.responses.ResponsesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "uploads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.uploads", "name": "uploads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of OpenAIWithRawResponse", "ret_type": "openai.resources.uploads.uploads.UploadsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.uploads", "name": "uploads", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of OpenAIWithRawResponse", "ret_type": "openai.resources.uploads.uploads.UploadsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "vector_stores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithRawResponse.vector_stores", "name": "vector_stores", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of OpenAIWithRawResponse", "ret_type": "openai.resources.vector_stores.vector_stores.VectorStoresWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithRawResponse.vector_stores", "name": "vector_stores", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of OpenAIWithRawResponse", "ret_type": "openai.resources.vector_stores.vector_stores.VectorStoresWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.OpenAIWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.OpenAIWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OpenAIWithStreamedResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai._client.OpenAIWithStreamedResponse", "name": "OpenAIWithStreamedResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai._client.OpenAIWithStreamedResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai._client", "mro": ["openai._client.OpenAIWithStreamedResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "client"], "arg_types": ["openai._client.OpenAIWithStreamedResponse", "openai._client.OpenAI"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of OpenAIWithStreamedResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai._client.OpenAIWithStreamedResponse._client", "name": "_client", "setter_type": null, "type": "openai._client.OpenAI"}}, "audio": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.audio", "name": "audio", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of OpenAIWithStreamedResponse", "ret_type": "openai.resources.audio.audio.AudioWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.audio", "name": "audio", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "audio of OpenAIWithStreamedResponse", "ret_type": "openai.resources.audio.audio.AudioWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "batches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.batches", "name": "batches", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of OpenAIWithStreamedResponse", "ret_type": "openai.resources.batches.BatchesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.batches", "name": "batches", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batches of OpenAIWithStreamedResponse", "ret_type": "openai.resources.batches.BatchesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "beta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.beta", "name": "beta", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of OpenAIWithStreamedResponse", "ret_type": "openai.resources.beta.beta.BetaWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.beta", "name": "beta", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "beta of OpenAIWithStreamedResponse", "ret_type": "openai.resources.beta.beta.BetaWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "chat": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.chat", "name": "chat", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of OpenAIWithStreamedResponse", "ret_type": "openai.resources.chat.chat.ChatWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.chat", "name": "chat", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "chat of OpenAIWithStreamedResponse", "ret_type": "openai.resources.chat.chat.ChatWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "completions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.completions", "name": "completions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of OpenAIWithStreamedResponse", "ret_type": "openai.resources.completions.CompletionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.completions", "name": "completions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "completions of OpenAIWithStreamedResponse", "ret_type": "openai.resources.completions.CompletionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "containers": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.containers", "name": "containers", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of OpenAIWithStreamedResponse", "ret_type": "openai.resources.containers.containers.ContainersWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.containers", "name": "containers", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "containers of OpenAIWithStreamedResponse", "ret_type": "openai.resources.containers.containers.ContainersWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "embeddings": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.embeddings", "name": "embeddings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of OpenAIWithStreamedResponse", "ret_type": "openai.resources.embeddings.EmbeddingsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.embeddings", "name": "embeddings", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embeddings of OpenAIWithStreamedResponse", "ret_type": "openai.resources.embeddings.EmbeddingsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "evals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.evals", "name": "evals", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of OpenAIWithStreamedResponse", "ret_type": "openai.resources.evals.evals.EvalsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.evals", "name": "evals", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "evals of OpenAIWithStreamedResponse", "ret_type": "openai.resources.evals.evals.EvalsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.files", "name": "files", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of OpenAIWithStreamedResponse", "ret_type": "openai.resources.files.FilesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.files", "name": "files", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "files of OpenAIWithStreamedResponse", "ret_type": "openai.resources.files.FilesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "fine_tuning": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.fine_tuning", "name": "fine_tuning", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of OpenAIWithStreamedResponse", "ret_type": "openai.resources.fine_tuning.fine_tuning.FineTuningWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.fine_tuning", "name": "fine_tuning", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "fine_tuning of OpenAIWithStreamedResponse", "ret_type": "openai.resources.fine_tuning.fine_tuning.FineTuningWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "images": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.images", "name": "images", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of OpenAIWithStreamedResponse", "ret_type": "openai.resources.images.ImagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.images", "name": "images", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "images of OpenAIWithStreamedResponse", "ret_type": "openai.resources.images.ImagesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.models", "name": "models", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of OpenAIWithStreamedResponse", "ret_type": "openai.resources.models.ModelsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.models", "name": "models", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "models of OpenAIWithStreamedResponse", "ret_type": "openai.resources.models.ModelsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "moderations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.moderations", "name": "moderations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of OpenAIWithStreamedResponse", "ret_type": "openai.resources.moderations.ModerationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.moderations", "name": "moderations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "moderations of OpenAIWithStreamedResponse", "ret_type": "openai.resources.moderations.ModerationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "responses": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.responses", "name": "responses", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of OpenAIWithStreamedResponse", "ret_type": "openai.resources.responses.responses.ResponsesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.responses", "name": "responses", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "responses of OpenAIWithStreamedResponse", "ret_type": "openai.resources.responses.responses.ResponsesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "uploads": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.uploads", "name": "uploads", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of OpenAIWithStreamedResponse", "ret_type": "openai.resources.uploads.uploads.UploadsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.uploads", "name": "uploads", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "uploads of OpenAIWithStreamedResponse", "ret_type": "openai.resources.uploads.uploads.UploadsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "vector_stores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai._client.OpenAIWithStreamedResponse.vector_stores", "name": "vector_stores", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of OpenAIWithStreamedResponse", "ret_type": "openai.resources.vector_stores.vector_stores.VectorStoresWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai._client.OpenAIWithStreamedResponse.vector_stores", "name": "vector_stores", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai._client.OpenAIWithStreamedResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "vector_stores of OpenAIWithStreamedResponse", "ret_type": "openai.resources.vector_stores.vector_stores.VectorStoresWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai._client.OpenAIWithStreamedResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai._client.OpenAIWithStreamedResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ProxiesTypes": {".class": "SymbolTableNode", "cross_ref": "openai._types.ProxiesTypes", "kind": "Gdef"}, "Querystring": {".class": "SymbolTableNode", "cross_ref": "openai._qs.Querystring", "kind": "Gdef", "module_public": false}, "RequestOptions": {".class": "SymbolTableNode", "cross_ref": "openai._types.RequestOptions", "kind": "Gdef"}, "Responses": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.responses.Responses", "kind": "Gdef", "module_public": false}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef", "module_public": false}, "Stream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.Stream", "kind": "Gdef", "module_public": false}, "SyncAPIClient": {".class": "SymbolTableNode", "cross_ref": "openai._base_client.SyncAPIClient", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Timeout": {".class": "SymbolTableNode", "cross_ref": "httpx._config.Timeout", "kind": "Gdef"}, "Transport": {".class": "SymbolTableNode", "cross_ref": "openai._types.Transport", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "Uploads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.Uploads", "kind": "Gdef", "module_public": false}, "VectorStores": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.VectorStores", "kind": "Gdef", "module_public": false}, "Webhooks": {".class": "SymbolTableNode", "cross_ref": "openai.resources.webhooks.Webhooks", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai._client.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai._client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "cross_ref": "openai._version.__version__", "kind": "Gdef", "module_public": false}, "_exceptions": {".class": "SymbolTableNode", "cross_ref": "openai._exceptions", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "audio": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio", "kind": "Gdef", "module_public": false}, "batches": {".class": "SymbolTableNode", "cross_ref": "openai.resources.batches", "kind": "Gdef", "module_public": false}, "beta": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "openai._compat.cached_property", "kind": "Gdef", "module_public": false}, "chat": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat", "kind": "Gdef", "module_public": false}, "completions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.completions", "kind": "Gdef", "module_public": false}, "containers": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers", "kind": "Gdef", "module_public": false}, "embeddings": {".class": "SymbolTableNode", "cross_ref": "openai.resources.embeddings", "kind": "Gdef", "module_public": false}, "evals": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals", "kind": "Gdef", "module_public": false}, "files": {".class": "SymbolTableNode", "cross_ref": "openai.resources.files", "kind": "Gdef", "module_public": false}, "fine_tuning": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning", "kind": "Gdef", "module_public": false}, "get_async_library": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.get_async_library", "kind": "Gdef", "module_public": false}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef", "module_public": false}, "images": {".class": "SymbolTableNode", "cross_ref": "openai.resources.images", "kind": "Gdef", "module_public": false}, "is_given": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_given", "kind": "Gdef", "module_public": false}, "is_mapping": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_mapping", "kind": "Gdef", "module_public": false}, "models": {".class": "SymbolTableNode", "cross_ref": "openai.resources.models", "kind": "Gdef", "module_public": false}, "moderations": {".class": "SymbolTableNode", "cross_ref": "openai.resources.moderations", "kind": "Gdef", "module_public": false}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef", "module_public": false}, "override": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.override", "kind": "Gdef", "module_public": false}, "responses": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses", "kind": "Gdef", "module_public": false}, "uploads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads", "kind": "Gdef", "module_public": false}, "vector_stores": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_client.py"}