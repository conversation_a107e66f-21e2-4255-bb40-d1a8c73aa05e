{".class": "MypyFile", "_fullname": "openai.resources.audio.audio", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.AsyncAPIResource", "kind": "Gdef", "module_public": false}, "AsyncAudio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.AsyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.audio.AsyncAudio", "name": "AsyncAudio", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.audio.AsyncAudio", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.audio", "mro": ["openai.resources.audio.audio.AsyncAudio", "openai._resource.AsyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "speech": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudio.speech", "name": "speech", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "openai.resources.audio.speech.AsyncSpeech", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudio.speech", "name": "speech", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of <PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": "openai.resources.audio.speech.AsyncSpeech", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudio.transcriptions", "name": "transcriptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of AsyncAudio", "ret_type": "openai.resources.audio.transcriptions.AsyncTranscriptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudio.transcriptions", "name": "transcriptions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of AsyncAudio", "ret_type": "openai.resources.audio.transcriptions.AsyncTranscriptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "translations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudio.translations", "name": "translations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of AsyncAudio", "ret_type": "openai.resources.audio.translations.AsyncTranslations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudio.translations", "name": "translations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of AsyncAudio", "ret_type": "openai.resources.audio.translations.AsyncTranslations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudio.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncAudio", "ret_type": "openai.resources.audio.audio.AsyncAudioWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudio.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncAudio", "ret_type": "openai.resources.audio.audio.AsyncAudioWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudio.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncAudio", "ret_type": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudio.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncAudio", "ret_type": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.audio.AsyncAudio.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.audio.AsyncAudio", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAudioWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.audio.AsyncAudioWithRawResponse", "name": "AsyncAudioWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.audio.AsyncAudioWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.audio", "mro": ["openai.resources.audio.audio.AsyncAudioWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "audio"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudioWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "audio"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithRawResponse", "openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncAudioWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_audio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudioWithRawResponse._audio", "name": "_audio", "setter_type": null, "type": "openai.resources.audio.audio.AsyncAudio"}}, "speech": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudioWithRawResponse.speech", "name": "speech", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of AsyncAudioWithRawResponse", "ret_type": "openai.resources.audio.speech.AsyncSpeechWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudioWithRawResponse.speech", "name": "speech", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of AsyncAudioWithRawResponse", "ret_type": "openai.resources.audio.speech.AsyncSpeechWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudioWithRawResponse.transcriptions", "name": "transcriptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of AsyncAudioWithRawResponse", "ret_type": "openai.resources.audio.transcriptions.AsyncTranscriptionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudioWithRawResponse.transcriptions", "name": "transcriptions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of AsyncAudioWithRawResponse", "ret_type": "openai.resources.audio.transcriptions.AsyncTranscriptionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "translations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudioWithRawResponse.translations", "name": "translations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of AsyncAudioWithRawResponse", "ret_type": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudioWithRawResponse.translations", "name": "translations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of AsyncAudioWithRawResponse", "ret_type": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.audio.AsyncAudioWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.audio.AsyncAudioWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAudioWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse", "name": "AsyncAudioWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.audio", "mro": ["openai.resources.audio.audio.AsyncAudioWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "audio"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "audio"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithStreamingResponse", "openai.resources.audio.audio.AsyncAudio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncAudioWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_audio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse._audio", "name": "_audio", "setter_type": null, "type": "openai.resources.audio.audio.AsyncAudio"}}, "speech": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse.speech", "name": "speech", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of AsyncAudioWithStreamingResponse", "ret_type": "openai.resources.audio.speech.AsyncSpeechWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse.speech", "name": "speech", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of AsyncAudioWithStreamingResponse", "ret_type": "openai.resources.audio.speech.AsyncSpeechWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse.transcriptions", "name": "transcriptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of AsyncAudioWithStreamingResponse", "ret_type": "openai.resources.audio.transcriptions.AsyncTranscriptionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse.transcriptions", "name": "transcriptions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of AsyncAudioWithStreamingResponse", "ret_type": "openai.resources.audio.transcriptions.AsyncTranscriptionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "translations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse.translations", "name": "translations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of AsyncAudioWithStreamingResponse", "ret_type": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse.translations", "name": "translations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AsyncAudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of AsyncAudioWithStreamingResponse", "ret_type": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncSpeech": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.AsyncSpeech", "kind": "Gdef", "module_public": false}, "AsyncSpeechWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.AsyncSpeechWithRawResponse", "kind": "Gdef", "module_public": false}, "AsyncSpeechWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.AsyncSpeechWithStreamingResponse", "kind": "Gdef", "module_public": false}, "AsyncTranscriptions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.AsyncTranscriptions", "kind": "Gdef", "module_public": false}, "AsyncTranscriptionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.AsyncTranscriptionsWithRawResponse", "kind": "Gdef", "module_public": false}, "AsyncTranscriptionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.AsyncTranscriptionsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "AsyncTranslations": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.AsyncTranslations", "kind": "Gdef", "module_public": false}, "AsyncTranslationsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse", "kind": "Gdef", "module_public": false}, "AsyncTranslationsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "Audio": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.SyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.audio.Audio", "name": "Audio", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.audio.Audio", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.audio", "mro": ["openai.resources.audio.audio.Audio", "openai._resource.SyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "speech": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.Audio.speech", "name": "speech", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of Audio", "ret_type": "openai.resources.audio.speech.Speech", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.Audio.speech", "name": "speech", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of Audio", "ret_type": "openai.resources.audio.speech.Speech", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.Audio.transcriptions", "name": "transcriptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of Audio", "ret_type": "openai.resources.audio.transcriptions.Transcriptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.Audio.transcriptions", "name": "transcriptions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of Audio", "ret_type": "openai.resources.audio.transcriptions.Transcriptions", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "translations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.Audio.translations", "name": "translations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of Audio", "ret_type": "openai.resources.audio.translations.Translations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.Audio.translations", "name": "translations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of Audio", "ret_type": "openai.resources.audio.translations.Translations", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.Audio.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Audio", "ret_type": "openai.resources.audio.audio.AudioWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.Audio.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Audio", "ret_type": "openai.resources.audio.audio.AudioWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.Audio.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Audio", "ret_type": "openai.resources.audio.audio.AudioWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.Audio.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Audio", "ret_type": "openai.resources.audio.audio.AudioWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.audio.Audio.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.audio.Audio", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AudioWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.audio.AudioWithRawResponse", "name": "AudioWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.audio.AudioWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.audio", "mro": ["openai.resources.audio.audio.AudioWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "audio"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.audio.audio.AudioWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "audio"], "arg_types": ["openai.resources.audio.audio.AudioWithRawResponse", "openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AudioWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_audio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.audio.AudioWithRawResponse._audio", "name": "_audio", "setter_type": null, "type": "openai.resources.audio.audio.Audio"}}, "speech": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AudioWithRawResponse.speech", "name": "speech", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of AudioWithRawResponse", "ret_type": "openai.resources.audio.speech.SpeechWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AudioWithRawResponse.speech", "name": "speech", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of AudioWithRawResponse", "ret_type": "openai.resources.audio.speech.SpeechWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AudioWithRawResponse.transcriptions", "name": "transcriptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of AudioWithRawResponse", "ret_type": "openai.resources.audio.transcriptions.TranscriptionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AudioWithRawResponse.transcriptions", "name": "transcriptions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of AudioWithRawResponse", "ret_type": "openai.resources.audio.transcriptions.TranscriptionsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "translations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AudioWithRawResponse.translations", "name": "translations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of AudioWithRawResponse", "ret_type": "openai.resources.audio.translations.TranslationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AudioWithRawResponse.translations", "name": "translations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of AudioWithRawResponse", "ret_type": "openai.resources.audio.translations.TranslationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.audio.AudioWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.audio.AudioWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AudioWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.audio.AudioWithStreamingResponse", "name": "AudioWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.audio.AudioWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.audio", "mro": ["openai.resources.audio.audio.AudioWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "audio"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.audio.audio.AudioWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "audio"], "arg_types": ["openai.resources.audio.audio.AudioWithStreamingResponse", "openai.resources.audio.audio.Audio"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AudioWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_audio": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.audio.AudioWithStreamingResponse._audio", "name": "_audio", "setter_type": null, "type": "openai.resources.audio.audio.Audio"}}, "speech": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AudioWithStreamingResponse.speech", "name": "speech", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of AudioWithStreamingResponse", "ret_type": "openai.resources.audio.speech.SpeechWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AudioWithStreamingResponse.speech", "name": "speech", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "speech of AudioWithStreamingResponse", "ret_type": "openai.resources.audio.speech.SpeechWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transcriptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AudioWithStreamingResponse.transcriptions", "name": "transcriptions", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of AudioWithStreamingResponse", "ret_type": "openai.resources.audio.transcriptions.TranscriptionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AudioWithStreamingResponse.transcriptions", "name": "transcriptions", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transcriptions of AudioWithStreamingResponse", "ret_type": "openai.resources.audio.transcriptions.TranscriptionsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "translations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.audio.AudioWithStreamingResponse.translations", "name": "translations", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of AudioWithStreamingResponse", "ret_type": "openai.resources.audio.translations.TranslationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.audio.AudioWithStreamingResponse.translations", "name": "translations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.audio.AudioWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "translations of AudioWithStreamingResponse", "ret_type": "openai.resources.audio.translations.TranslationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.audio.AudioWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.audio.AudioWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Speech": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.Speech", "kind": "Gdef", "module_public": false}, "SpeechWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.SpeechWithRawResponse", "kind": "Gdef", "module_public": false}, "SpeechWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.speech.SpeechWithStreamingResponse", "kind": "Gdef", "module_public": false}, "SyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.SyncAPIResource", "kind": "Gdef", "module_public": false}, "Transcriptions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.Transcriptions", "kind": "Gdef", "module_public": false}, "TranscriptionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.TranscriptionsWithRawResponse", "kind": "Gdef", "module_public": false}, "TranscriptionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.transcriptions.TranscriptionsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "Translations": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.Translations", "kind": "Gdef", "module_public": false}, "TranslationsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.TranslationsWithRawResponse", "kind": "Gdef", "module_public": false}, "TranslationsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.translations.TranslationsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.audio.audio.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.audio.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.audio.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.audio.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.audio.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.audio.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.audio.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "openai._compat.cached_property", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\audio\\audio.py"}