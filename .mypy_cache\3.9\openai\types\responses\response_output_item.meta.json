{"data_mtime": 1754400649, "dep_lines": [8, 9, 10, 11, 12, 13, 14, 6, 7, 3, 4, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.responses.response_output_message", "openai.types.responses.response_reasoning_item", "openai.types.responses.response_computer_tool_call", "openai.types.responses.response_function_tool_call", "openai.types.responses.response_function_web_search", "openai.types.responses.response_file_search_tool_call", "openai.types.responses.response_code_interpreter_tool_call", "openai._utils", "openai._models", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "fbaa7941a9dcce83916dbf88e43b6d0ed7fc1b77", "id": "openai.types.responses.response_output_item", "ignore_all": true, "interface_hash": "a9c7a7961a71fff196fd2b5c3a3cc2b6149e3ace", "mtime": 1754241437, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_output_item.py", "plugin_data": null, "size": 4575, "suppressed": [], "version_id": "1.17.1"}