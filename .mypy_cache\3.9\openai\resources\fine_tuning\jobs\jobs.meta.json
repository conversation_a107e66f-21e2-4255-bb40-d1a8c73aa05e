{"data_mtime": 1754400651, "dep_lines": [14, 29, 29, 29, 30, 31, 32, 29, 10, 11, 12, 13, 22, 23, 24, 25, 3, 5, 6, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 20, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.fine_tuning.jobs.checkpoints", "openai.types.fine_tuning.job_list_params", "openai.types.fine_tuning.job_create_params", "openai.types.fine_tuning.job_list_events_params", "openai.types.shared_params.metadata", "openai.types.fine_tuning.fine_tuning_job", "openai.types.fine_tuning.fine_tuning_job_event", "openai.types.fine_tuning", "openai._legacy_response", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai.pagination", "openai._base_client", "__future__", "typing", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai.types", "openai.types.fine_tuning.dpo_hyperparameters_param", "openai.types.fine_tuning.dpo_method_param", "openai.types.fine_tuning.reinforcement_hyperparameters_param", "openai.types.fine_tuning.reinforcement_method_param", "openai.types.fine_tuning.supervised_hyperparameters_param", "openai.types.fine_tuning.supervised_method_param", "openai.types.graders", "openai.types.graders.label_model_grader_param", "openai.types.graders.multi_grader_param", "openai.types.graders.python_grader_param", "openai.types.graders.score_model_grader_param", "openai.types.graders.string_check_grader_param", "openai.types.graders.text_similarity_grader_param", "openai.types.responses", "openai.types.responses.response_input_text_param", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "0ab23990e6a72404540985f21ead6f3c2d84adfd", "id": "openai.resources.fine_tuning.jobs.jobs", "ignore_all": true, "interface_hash": "5c2460288e481629a05f6a064f584231c979de6f", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\jobs.py", "plugin_data": null, "size": 37237, "suppressed": [], "version_id": "1.17.1"}