{".class": "MypyFile", "_fullname": "openai.lib.streaming.responses._events", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "GenericModel": {".class": "SymbolTableNode", "cross_ref": "openai._compat.GenericModel", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ParsedResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponse", "kind": "Gdef"}, "PropertyInfo": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.PropertyInfo", "kind": "Gdef"}, "RawResponseCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_completed_event.ResponseCompletedEvent", "kind": "Gdef"}, "RawResponseFunctionCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_call_arguments_delta_event.ResponseFunctionCallArgumentsDeltaEvent", "kind": "Gdef"}, "RawResponseTextDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_text_delta_event.ResponseTextDeltaEvent", "kind": "Gdef"}, "RawResponseTextDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_text_done_event.ResponseTextDoneEvent", "kind": "Gdef"}, "ResponseAudioDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_delta_event.ResponseAudioDeltaEvent", "kind": "Gdef"}, "ResponseAudioDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_done_event.ResponseAudioDoneEvent", "kind": "Gdef"}, "ResponseAudioTranscriptDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_transcript_delta_event.ResponseAudioTranscriptDeltaEvent", "kind": "Gdef"}, "ResponseAudioTranscriptDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_transcript_done_event.ResponseAudioTranscriptDoneEvent", "kind": "Gdef"}, "ResponseCodeInterpreterCallCodeDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_code_delta_event.ResponseCodeInterpreterCallCodeDeltaEvent", "kind": "Gdef"}, "ResponseCodeInterpreterCallCodeDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_code_done_event.ResponseCodeInterpreterCallCodeDoneEvent", "kind": "Gdef"}, "ResponseCodeInterpreterCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_completed_event.ResponseCodeInterpreterCallCompletedEvent", "kind": "Gdef"}, "ResponseCodeInterpreterCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_in_progress_event.ResponseCodeInterpreterCallInProgressEvent", "kind": "Gdef"}, "ResponseCodeInterpreterCallInterpretingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_interpreting_event.ResponseCodeInterpreterCallInterpretingEvent", "kind": "Gdef"}, "ResponseCompletedEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.types.responses.response_completed_event.ResponseCompletedEvent", "openai._compat.GenericModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.responses._events.ResponseCompletedEvent", "name": "ResponseCompletedEvent", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseCompletedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._events.ResponseCompletedEvent", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 83, "name": "response", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseCompletedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 15, "name": "sequence_number", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 18, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "response.completed"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.lib.streaming.responses._events", "mro": ["openai.lib.streaming.responses._events.ResponseCompletedEvent", "openai.types.responses.response_completed_event.ResponseCompletedEvent", "openai._models.BaseModel", "openai._compat.GenericModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.lib.streaming.responses._events.ResponseCompletedEvent.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3], "arg_names": ["self", "_request_id", "response", "sequence_number", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._events.ResponseCompletedEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3], "arg_names": ["self", "_request_id", "response", "sequence_number", "type"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseCompletedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._events.ResponseCompletedEvent"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseCompletedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}, "builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.completed"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResponseCompletedEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "response", "sequence_number", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.lib.streaming.responses._events.ResponseCompletedEvent.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "response", "sequence_number", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseCompletedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}, "builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.completed"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ResponseCompletedEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.lib.streaming.responses._events.ResponseCompletedEvent.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "response", "sequence_number", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseCompletedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}, "builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.completed"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ResponseCompletedEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.lib.streaming.responses._events.ResponseCompletedEvent.response", "name": "response", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseCompletedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._events.ResponseCompletedEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseCompletedEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._events.ResponseCompletedEvent"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["TextFormatT"], "typeddict_type": null}}, "ResponseContentPartAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_content_part_added_event.ResponseContentPartAddedEvent", "kind": "Gdef"}, "ResponseContentPartDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_content_part_done_event.ResponseContentPartDoneEvent", "kind": "Gdef"}, "ResponseCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_created_event.ResponseCreatedEvent", "kind": "Gdef"}, "ResponseErrorEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_error_event.ResponseErrorEvent", "kind": "Gdef"}, "ResponseFailedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_failed_event.ResponseFailedEvent", "kind": "Gdef"}, "ResponseFileSearchCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_call_completed_event.ResponseFileSearchCallCompletedEvent", "kind": "Gdef"}, "ResponseFileSearchCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_call_in_progress_event.ResponseFileSearchCallInProgressEvent", "kind": "Gdef"}, "ResponseFileSearchCallSearchingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_call_searching_event.ResponseFileSearchCallSearchingEvent", "kind": "Gdef"}, "ResponseFunctionCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.types.responses.response_function_call_arguments_delta_event.ResponseFunctionCallArgumentsDeltaEvent"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent", "name": "ResponseFunctionCallArgumentsDeltaEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 11, "name": "delta", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 14, "name": "item_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 17, "name": "output_index", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 22, "name": "sequence_number", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 25, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "response.function_call_arguments.delta"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 79, "name": "snapshot", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.lib.streaming.responses._events", "mro": ["openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent", "openai.types.responses.response_function_call_arguments_delta_event.ResponseFunctionCallArgumentsDeltaEvent", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "delta", "item_id", "output_index", "sequence_number", "type", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "delta", "item_id", "output_index", "sequence_number", "type", "snapshot"], "arg_types": ["openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.function_call_arguments.delta"}, "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResponseFunctionCallArgumentsDeltaEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "delta", "item_id", "output_index", "sequence_number", "type", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "delta", "item_id", "output_index", "sequence_number", "type", "snapshot"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.function_call_arguments.delta"}, "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ResponseFunctionCallArgumentsDeltaEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "delta", "item_id", "output_index", "sequence_number", "type", "snapshot"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.function_call_arguments.delta"}, "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ResponseFunctionCallArgumentsDeltaEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent.snapshot", "name": "snapshot", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseFunctionCallArgumentsDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_call_arguments_done_event.ResponseFunctionCallArgumentsDoneEvent", "kind": "Gdef"}, "ResponseImageGenCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_completed_event.ResponseImageGenCallCompletedEvent", "kind": "Gdef"}, "ResponseImageGenCallGeneratingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_generating_event.ResponseImageGenCallGeneratingEvent", "kind": "Gdef"}, "ResponseImageGenCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_in_progress_event.ResponseImageGenCallInProgressEvent", "kind": "Gdef"}, "ResponseImageGenCallPartialImageEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_partial_image_event.ResponseImageGenCallPartialImageEvent", "kind": "Gdef"}, "ResponseInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_in_progress_event.ResponseInProgressEvent", "kind": "Gdef"}, "ResponseIncompleteEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_incomplete_event.ResponseIncompleteEvent", "kind": "Gdef"}, "ResponseMcpCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_arguments_delta_event.ResponseMcpCallArgumentsDeltaEvent", "kind": "Gdef"}, "ResponseMcpCallArgumentsDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_arguments_done_event.ResponseMcpCallArgumentsDoneEvent", "kind": "Gdef"}, "ResponseMcpCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_completed_event.ResponseMcpCallCompletedEvent", "kind": "Gdef"}, "ResponseMcpCallFailedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_failed_event.ResponseMcpCallFailedEvent", "kind": "Gdef"}, "ResponseMcpCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_in_progress_event.ResponseMcpCallInProgressEvent", "kind": "Gdef"}, "ResponseMcpListToolsCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_list_tools_completed_event.ResponseMcpListToolsCompletedEvent", "kind": "Gdef"}, "ResponseMcpListToolsFailedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_list_tools_failed_event.ResponseMcpListToolsFailedEvent", "kind": "Gdef"}, "ResponseMcpListToolsInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_list_tools_in_progress_event.ResponseMcpListToolsInProgressEvent", "kind": "Gdef"}, "ResponseOutputItemAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item_added_event.ResponseOutputItemAddedEvent", "kind": "Gdef"}, "ResponseOutputItemDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item_done_event.ResponseOutputItemDoneEvent", "kind": "Gdef"}, "ResponseOutputTextAnnotationAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_text_annotation_added_event.ResponseOutputTextAnnotationAddedEvent", "kind": "Gdef"}, "ResponseQueuedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_queued_event.ResponseQueuedEvent", "kind": "Gdef"}, "ResponseReasoningSummaryDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_delta_event.ResponseReasoningSummaryDeltaEvent", "kind": "Gdef"}, "ResponseReasoningSummaryDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_done_event.ResponseReasoningSummaryDoneEvent", "kind": "Gdef"}, "ResponseReasoningSummaryPartAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_part_added_event.ResponseReasoningSummaryPartAddedEvent", "kind": "Gdef"}, "ResponseReasoningSummaryPartDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_part_done_event.ResponseReasoningSummaryPartDoneEvent", "kind": "Gdef"}, "ResponseReasoningSummaryTextDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_text_delta_event.ResponseReasoningSummaryTextDeltaEvent", "kind": "Gdef"}, "ResponseReasoningSummaryTextDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_text_done_event.ResponseReasoningSummaryTextDoneEvent", "kind": "Gdef"}, "ResponseRefusalDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_refusal_delta_event.ResponseRefusalDeltaEvent", "kind": "Gdef"}, "ResponseRefusalDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_refusal_done_event.ResponseRefusalDoneEvent", "kind": "Gdef"}, "ResponseStreamEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseStreamEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "openai.lib.streaming.responses._events.ResponseStreamEvent", "line": 86, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["openai.lib.streaming.responses._events.ResponseTextDeltaEvent", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseStreamEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._events.ResponseTextDoneEvent"}, "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseStreamEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._events.ResponseCompletedEvent"}, "openai.types.responses.response_audio_delta_event.ResponseAudioDeltaEvent", "openai.types.responses.response_audio_done_event.ResponseAudioDoneEvent", "openai.types.responses.response_audio_transcript_delta_event.ResponseAudioTranscriptDeltaEvent", "openai.types.responses.response_audio_transcript_done_event.ResponseAudioTranscriptDoneEvent", "openai.types.responses.response_code_interpreter_call_code_delta_event.ResponseCodeInterpreterCallCodeDeltaEvent", "openai.types.responses.response_code_interpreter_call_code_done_event.ResponseCodeInterpreterCallCodeDoneEvent", "openai.types.responses.response_code_interpreter_call_completed_event.ResponseCodeInterpreterCallCompletedEvent", "openai.types.responses.response_code_interpreter_call_in_progress_event.ResponseCodeInterpreterCallInProgressEvent", "openai.types.responses.response_code_interpreter_call_interpreting_event.ResponseCodeInterpreterCallInterpretingEvent", "openai.types.responses.response_content_part_added_event.ResponseContentPartAddedEvent", "openai.types.responses.response_content_part_done_event.ResponseContentPartDoneEvent", "openai.types.responses.response_created_event.ResponseCreatedEvent", "openai.types.responses.response_error_event.ResponseErrorEvent", "openai.types.responses.response_file_search_call_completed_event.ResponseFileSearchCallCompletedEvent", "openai.types.responses.response_file_search_call_in_progress_event.ResponseFileSearchCallInProgressEvent", "openai.types.responses.response_file_search_call_searching_event.ResponseFileSearchCallSearchingEvent", "openai.types.responses.response_function_call_arguments_done_event.ResponseFunctionCallArgumentsDoneEvent", "openai.types.responses.response_in_progress_event.ResponseInProgressEvent", "openai.types.responses.response_failed_event.ResponseFailedEvent", "openai.types.responses.response_incomplete_event.ResponseIncompleteEvent", "openai.types.responses.response_output_item_added_event.ResponseOutputItemAddedEvent", "openai.types.responses.response_output_item_done_event.ResponseOutputItemDoneEvent", "openai.types.responses.response_refusal_delta_event.ResponseRefusalDeltaEvent", "openai.types.responses.response_refusal_done_event.ResponseRefusalDoneEvent", {".class": "Instance", "args": [{".class": "NoneType"}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._events.ResponseTextDoneEvent"}, "openai.types.responses.response_web_search_call_completed_event.ResponseWebSearchCallCompletedEvent", "openai.types.responses.response_web_search_call_in_progress_event.ResponseWebSearchCallInProgressEvent", "openai.types.responses.response_web_search_call_searching_event.ResponseWebSearchCallSearchingEvent", "openai.types.responses.response_reasoning_summary_part_added_event.ResponseReasoningSummaryPartAddedEvent", "openai.types.responses.response_reasoning_summary_part_done_event.ResponseReasoningSummaryPartDoneEvent", "openai.types.responses.response_reasoning_summary_text_delta_event.ResponseReasoningSummaryTextDeltaEvent", "openai.types.responses.response_reasoning_summary_text_done_event.ResponseReasoningSummaryTextDoneEvent", "openai.types.responses.response_image_gen_call_completed_event.ResponseImageGenCallCompletedEvent", "openai.types.responses.response_image_gen_call_in_progress_event.ResponseImageGenCallInProgressEvent", "openai.types.responses.response_image_gen_call_generating_event.ResponseImageGenCallGeneratingEvent", "openai.types.responses.response_image_gen_call_partial_image_event.ResponseImageGenCallPartialImageEvent", "openai.types.responses.response_mcp_call_completed_event.ResponseMcpCallCompletedEvent", "openai.types.responses.response_mcp_call_arguments_delta_event.ResponseMcpCallArgumentsDeltaEvent", "openai.types.responses.response_mcp_call_arguments_done_event.ResponseMcpCallArgumentsDoneEvent", "openai.types.responses.response_mcp_call_failed_event.ResponseMcpCallFailedEvent", "openai.types.responses.response_mcp_call_in_progress_event.ResponseMcpCallInProgressEvent", "openai.types.responses.response_mcp_list_tools_completed_event.ResponseMcpListToolsCompletedEvent", "openai.types.responses.response_mcp_list_tools_failed_event.ResponseMcpListToolsFailedEvent", "openai.types.responses.response_mcp_list_tools_in_progress_event.ResponseMcpListToolsInProgressEvent", "openai.types.responses.response_output_text_annotation_added_event.ResponseOutputTextAnnotationAddedEvent", "openai.types.responses.response_queued_event.ResponseQueuedEvent", "openai.types.responses.response_reasoning_summary_delta_event.ResponseReasoningSummaryDeltaEvent", "openai.types.responses.response_reasoning_summary_done_event.ResponseReasoningSummaryDoneEvent"], "uses_pep604_syntax": false}}}, "ResponseTextDeltaEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.types.responses.response_text_delta_event.ResponseTextDeltaEvent"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.responses._events.ResponseTextDeltaEvent", "name": "ResponseTextDeltaEvent", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._events.ResponseTextDeltaEvent", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 31, "name": "content_index", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 34, "name": "delta", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 37, "name": "item_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 40, "name": "logprobs", "type": {".class": "Instance", "args": ["openai.types.responses.response_text_delta_event.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 43, "name": "output_index", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 46, "name": "sequence_number", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 49, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "response.output_text.delta"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 71, "name": "snapshot", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.lib.streaming.responses._events", "mro": ["openai.lib.streaming.responses._events.ResponseTextDeltaEvent", "openai.types.responses.response_text_delta_event.ResponseTextDeltaEvent", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.lib.streaming.responses._events.ResponseTextDeltaEvent.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "content_index", "delta", "item_id", "logprobs", "output_index", "sequence_number", "type", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._events.ResponseTextDeltaEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "content_index", "delta", "item_id", "logprobs", "output_index", "sequence_number", "type", "snapshot"], "arg_types": ["openai.lib.streaming.responses._events.ResponseTextDeltaEvent", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", "builtins.str", {".class": "Instance", "args": ["openai.types.responses.response_text_delta_event.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.output_text.delta"}, "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResponseTextDeltaEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "content_index", "delta", "item_id", "logprobs", "output_index", "sequence_number", "type", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.lib.streaming.responses._events.ResponseTextDeltaEvent.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "content_index", "delta", "item_id", "logprobs", "output_index", "sequence_number", "type", "snapshot"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", "builtins.str", {".class": "Instance", "args": ["openai.types.responses.response_text_delta_event.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.output_text.delta"}, "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ResponseTextDeltaEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.lib.streaming.responses._events.ResponseTextDeltaEvent.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "content_index", "delta", "item_id", "logprobs", "output_index", "sequence_number", "type", "snapshot"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", "builtins.str", {".class": "Instance", "args": ["openai.types.responses.response_text_delta_event.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.output_text.delta"}, "builtins.str"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ResponseTextDeltaEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.lib.streaming.responses._events.ResponseTextDeltaEvent.snapshot", "name": "snapshot", "setter_type": null, "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._events.ResponseTextDeltaEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.lib.streaming.responses._events.ResponseTextDeltaEvent", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponseTextDoneEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.types.responses.response_text_done_event.ResponseTextDoneEvent", "openai._compat.GenericModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "name": "ResponseTextDoneEvent", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 31, "name": "content_index", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 34, "name": "item_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 37, "name": "logprobs", "type": {".class": "Instance", "args": ["openai.types.responses.response_text_done_event.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 40, "name": "output_index", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 43, "name": "sequence_number", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 46, "name": "text", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 49, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "response.output_text.done"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 75, "name": "parsed", "type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.lib.streaming.responses._events", "mro": ["openai.lib.streaming.responses._events.ResponseTextDoneEvent", "openai.types.responses.response_text_done_event.ResponseTextDoneEvent", "openai._models.BaseModel", "openai._compat.GenericModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.lib.streaming.responses._events.ResponseTextDoneEvent.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 5], "arg_names": ["self", "_request_id", "content_index", "item_id", "logprobs", "output_index", "sequence_number", "text", "type", "parsed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._events.ResponseTextDoneEvent.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 5], "arg_names": ["self", "_request_id", "content_index", "item_id", "logprobs", "output_index", "sequence_number", "text", "type", "parsed"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._events.ResponseTextDoneEvent"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["openai.types.responses.response_text_done_event.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.output_text.done"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResponseTextDoneEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "content_index", "item_id", "logprobs", "output_index", "sequence_number", "text", "type", "parsed"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.lib.streaming.responses._events.ResponseTextDoneEvent.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "content_index", "item_id", "logprobs", "output_index", "sequence_number", "text", "type", "parsed"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["openai.types.responses.response_text_done_event.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.output_text.done"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ResponseTextDoneEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.lib.streaming.responses._events.ResponseTextDoneEvent.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "content_index", "item_id", "logprobs", "output_index", "sequence_number", "text", "type", "parsed"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.int", "builtins.str", {".class": "Instance", "args": ["openai.types.responses.response_text_done_event.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.int", "builtins.int", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "response.output_text.done"}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ResponseTextDoneEvent", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "parsed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.lib.streaming.responses._events.ResponseTextDoneEvent.parsed", "name": "parsed", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._events.ResponseTextDoneEvent.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._events.ResponseTextDoneEvent"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["TextFormatT"], "typeddict_type": null}}, "ResponseWebSearchCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_web_search_call_completed_event.ResponseWebSearchCallCompletedEvent", "kind": "Gdef"}, "ResponseWebSearchCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_web_search_call_in_progress_event.ResponseWebSearchCallInProgressEvent", "kind": "Gdef"}, "ResponseWebSearchCallSearchingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_web_search_call_searching_event.ResponseWebSearchCallSearchingEvent", "kind": "Gdef"}, "TextFormatT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "NoneType"}, "fullname": "openai.lib.streaming.responses._events.TextFormatT", "name": "TextFormatT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVar", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._events.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._events.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._events.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._events.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._events.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._events.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_events.py"}