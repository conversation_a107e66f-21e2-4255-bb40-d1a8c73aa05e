{"data_mtime": 1754409788, "dep_lines": [6, 7, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30, 30], "dependencies": ["dataclasses", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum"], "hash": "16dde607083bd4aabf7c0b18314fb7ec3e8bd73e", "id": "config.interfaces", "ignore_all": false, "interface_hash": "3b43c1299441253aacbb74fcb488cdc3b6e1fa17", "mtime": 1754409788, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\config\\interfaces.py", "plugin_data": null, "size": 1311, "suppressed": [], "version_id": "1.17.1"}