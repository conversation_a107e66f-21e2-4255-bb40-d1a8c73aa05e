{"data_mtime": 1754400651, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 21, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.webhooks.unwrap_webhook_event", "openai.types.webhooks.batch_failed_webhook_event", "openai.types.webhooks.batch_expired_webhook_event", "openai.types.webhooks.batch_cancelled_webhook_event", "openai.types.webhooks.batch_completed_webhook_event", "openai.types.webhooks.eval_run_failed_webhook_event", "openai.types.webhooks.response_failed_webhook_event", "openai.types.webhooks.eval_run_canceled_webhook_event", "openai.types.webhooks.eval_run_succeeded_webhook_event", "openai.types.webhooks.response_cancelled_webhook_event", "openai.types.webhooks.response_completed_webhook_event", "openai.types.webhooks.response_incomplete_webhook_event", "openai.types.webhooks.fine_tuning_job_failed_webhook_event", "openai.types.webhooks.fine_tuning_job_cancelled_webhook_event", "openai.types.webhooks.fine_tuning_job_succeeded_webhook_event", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "41daeb39d700bce0e1c733774da34d7abf3ea61e", "id": "openai.types.webhooks", "ignore_all": true, "interface_hash": "4a21bc9a719577de8927d9d86b5773ca608656d4", "mtime": 1754241437, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\__init__.py", "plugin_data": null, "size": 1709, "suppressed": [], "version_id": "1.17.1"}