{".class": "MypyFile", "_fullname": "google.generativeai.models", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.models.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.models.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.models.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.models.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.models.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.models.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_apply_update": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["thing", "path", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.models._apply_update", "name": "_apply_update", "type": null}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "create_tuned_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["source_model", "training_data", "id", "display_name", "description", "temperature", "top_p", "top_k", "epoch_count", "batch_size", "learning_rate", "input_key", "output_key", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.models.create_tuned_model", "name": "create_tuned_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["source_model", "training_data", "id", "display_name", "description", "temperature", "top_p", "top_k", "epoch_count", "batch_size", "learning_rate", "input_key", "output_key", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.AnyModelNameOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.TuningDataOptions"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.str", "builtins.str", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_tuned_model", "ret_type": "google.generativeai.operations.CreateTunedModelOperation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_tuned_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["tuned_model", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.models.delete_tuned_model", "name": "delete_tuned_model", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["tuned_model", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.TunedModelNameOptions"}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_tuned_model", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "field_mask_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.generativeai.models.field_mask_pb2", "name": "field_mask_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.generativeai.models.field_mask_pb2", "source_any": null, "type_of_any": 3}}}, "flatten_update_paths": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.utils.flatten_update_paths", "kind": "Gdef"}, "get_base_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["name", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.models.get_base_model", "name": "get_base_model", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["name", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.BaseModelNameOptions"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_base_model", "ret_type": "google.generativeai.types.model_types.Model", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_base_model_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["model", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.models.get_base_model_name", "name": "get_base_model_name", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["model", "client"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.AnyModelNameOptions"}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_base_model_name", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_default_model_client": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.client.get_default_model_client", "kind": "Gdef"}, "get_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["name", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.models.get_model", "name": "get_model", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["name", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.AnyModelNameOptions"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_model", "ret_type": {".class": "UnionType", "items": ["google.generativeai.types.model_types.Model", "google.generativeai.types.model_types.TunedModel"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_tuned_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["name", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.models.get_tuned_model", "name": "get_tuned_model", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["name", "client", "request_options"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.TunedModelNameOptions"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_tuned_model", "ret_type": "google.generativeai.types.model_types.TunedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "glm": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage", "kind": "Gdef"}, "helper_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.helper_types", "kind": "Gdef"}, "list_models": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["page_size", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.models.list_models", "name": "list_models", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["page_size", "client", "request_options"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_models", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.ModelsIterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_tuned_models": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["page_size", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.models.list_tuned_models", "name": "list_tuned_models", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["page_size", "client", "request_options"], "arg_types": [{".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_tuned_models", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.model_types.TunedModelsIterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model_types": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.types.model_types", "kind": "Gdef"}, "operation": {".class": "SymbolTableNode", "cross_ref": "google.api_core.operation", "kind": "Gdef"}, "operations": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.operations", "kind": "Gdef"}, "protobuf_helpers": {".class": "SymbolTableNode", "cross_ref": "google.api_core.protobuf_helpers", "kind": "Gdef"}, "protos": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.protos", "kind": "Gdef"}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef"}, "update_tuned_model": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "google.generativeai.models.update_tuned_model", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5], "arg_names": ["tuned_model", "updates", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "google.generativeai.models.update_tuned_model", "name": "update_tuned_model", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5], "arg_names": ["tuned_model", "updates", "client", "request_options"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "google.ai.generativelanguage_v1beta.types.tuned_model.TunedModel"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_tuned_model", "ret_type": "google.generativeai.types.model_types.TunedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5], "arg_names": ["tuned_model", "updates", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "google.generativeai.models.update_tuned_model", "name": "update_tuned_model", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5], "arg_names": ["tuned_model", "updates", "client", "request_options"], "arg_types": ["google.ai.generativelanguage_v1beta.types.tuned_model.TunedModel", {".class": "NoneType"}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_tuned_model", "ret_type": "google.generativeai.types.model_types.TunedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "google.generativeai.models.update_tuned_model", "name": "update_tuned_model", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5], "arg_names": ["tuned_model", "updates", "client", "request_options"], "arg_types": ["google.ai.generativelanguage_v1beta.types.tuned_model.TunedModel", {".class": "NoneType"}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_tuned_model", "ret_type": "google.generativeai.types.model_types.TunedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": ["tuned_model", "updates", "client", "request_options"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "google.generativeai.models.update_tuned_model", "name": "update_tuned_model", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["tuned_model", "updates", "client", "request_options"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_tuned_model", "ret_type": "google.generativeai.types.model_types.TunedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "google.generativeai.models.update_tuned_model", "name": "update_tuned_model", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["tuned_model", "updates", "client", "request_options"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_tuned_model", "ret_type": "google.generativeai.types.model_types.TunedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 1, 5, 5], "arg_names": ["tuned_model", "updates", "client", "request_options"], "arg_types": ["google.ai.generativelanguage_v1beta.types.tuned_model.TunedModel", {".class": "NoneType"}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_tuned_model", "ret_type": "google.generativeai.types.model_types.TunedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": ["tuned_model", "updates", "client", "request_options"], "arg_types": ["builtins.str", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.helper_types.RequestOptionsType"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_tuned_model", "ret_type": "google.generativeai.types.model_types.TunedModel", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\models.py"}