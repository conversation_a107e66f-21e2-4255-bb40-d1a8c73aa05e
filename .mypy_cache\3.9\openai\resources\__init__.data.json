{".class": "MypyFile", "_fullname": "openai.resources", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAudio": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.AsyncAudio", "kind": "Gdef"}, "AsyncAudioWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.AsyncAudioWithRawResponse", "kind": "Gdef"}, "AsyncAudioWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.AsyncAudioWithStreamingResponse", "kind": "Gdef"}, "AsyncBatches": {".class": "SymbolTableNode", "cross_ref": "openai.resources.batches.AsyncBatches", "kind": "Gdef"}, "AsyncBatchesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.batches.AsyncBatchesWithRawResponse", "kind": "Gdef"}, "AsyncBatchesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.batches.AsyncBatchesWithStreamingResponse", "kind": "Gdef"}, "AsyncBeta": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.AsyncBeta", "kind": "Gdef"}, "AsyncBetaWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.AsyncBetaWithRawResponse", "kind": "Gdef"}, "AsyncBetaWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.AsyncBetaWithStreamingResponse", "kind": "Gdef"}, "AsyncChat": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.AsyncChat", "kind": "Gdef"}, "AsyncChatWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.AsyncChatWithRawResponse", "kind": "Gdef"}, "AsyncChatWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.AsyncChatWithStreamingResponse", "kind": "Gdef"}, "AsyncCompletions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.completions.AsyncCompletions", "kind": "Gdef"}, "AsyncCompletionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.completions.AsyncCompletionsWithRawResponse", "kind": "Gdef"}, "AsyncCompletionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.completions.AsyncCompletionsWithStreamingResponse", "kind": "Gdef"}, "AsyncContainers": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.AsyncContainers", "kind": "Gdef"}, "AsyncContainersWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.AsyncContainersWithRawResponse", "kind": "Gdef"}, "AsyncContainersWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.AsyncContainersWithStreamingResponse", "kind": "Gdef"}, "AsyncEmbeddings": {".class": "SymbolTableNode", "cross_ref": "openai.resources.embeddings.AsyncEmbeddings", "kind": "Gdef"}, "AsyncEmbeddingsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.embeddings.AsyncEmbeddingsWithRawResponse", "kind": "Gdef"}, "AsyncEmbeddingsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.embeddings.AsyncEmbeddingsWithStreamingResponse", "kind": "Gdef"}, "AsyncEvals": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.AsyncEvals", "kind": "Gdef"}, "AsyncEvalsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.AsyncEvalsWithRawResponse", "kind": "Gdef"}, "AsyncEvalsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse", "kind": "Gdef"}, "AsyncFiles": {".class": "SymbolTableNode", "cross_ref": "openai.resources.files.AsyncFiles", "kind": "Gdef"}, "AsyncFilesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.files.AsyncFilesWithRawResponse", "kind": "Gdef"}, "AsyncFilesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.files.AsyncFilesWithStreamingResponse", "kind": "Gdef"}, "AsyncFineTuning": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuning", "kind": "Gdef"}, "AsyncFineTuningWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuningWithRawResponse", "kind": "Gdef"}, "AsyncFineTuningWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.AsyncFineTuningWithStreamingResponse", "kind": "Gdef"}, "AsyncImages": {".class": "SymbolTableNode", "cross_ref": "openai.resources.images.AsyncImages", "kind": "Gdef"}, "AsyncImagesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.images.AsyncImagesWithRawResponse", "kind": "Gdef"}, "AsyncImagesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.images.AsyncImagesWithStreamingResponse", "kind": "Gdef"}, "AsyncModels": {".class": "SymbolTableNode", "cross_ref": "openai.resources.models.AsyncModels", "kind": "Gdef"}, "AsyncModelsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.models.AsyncModelsWithRawResponse", "kind": "Gdef"}, "AsyncModelsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.models.AsyncModelsWithStreamingResponse", "kind": "Gdef"}, "AsyncModerations": {".class": "SymbolTableNode", "cross_ref": "openai.resources.moderations.AsyncModerations", "kind": "Gdef"}, "AsyncModerationsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.moderations.AsyncModerationsWithRawResponse", "kind": "Gdef"}, "AsyncModerationsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.moderations.AsyncModerationsWithStreamingResponse", "kind": "Gdef"}, "AsyncUploads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.AsyncUploads", "kind": "Gdef"}, "AsyncUploadsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.AsyncUploadsWithRawResponse", "kind": "Gdef"}, "AsyncUploadsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.AsyncUploadsWithStreamingResponse", "kind": "Gdef"}, "AsyncVectorStores": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.AsyncVectorStores", "kind": "Gdef"}, "AsyncVectorStoresWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.AsyncVectorStoresWithRawResponse", "kind": "Gdef"}, "AsyncVectorStoresWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.AsyncVectorStoresWithStreamingResponse", "kind": "Gdef"}, "Audio": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.Audio", "kind": "Gdef"}, "AudioWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.AudioWithRawResponse", "kind": "Gdef"}, "AudioWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.audio.audio.AudioWithStreamingResponse", "kind": "Gdef"}, "Batches": {".class": "SymbolTableNode", "cross_ref": "openai.resources.batches.Batches", "kind": "Gdef"}, "BatchesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.batches.BatchesWithRawResponse", "kind": "Gdef"}, "BatchesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.batches.BatchesWithStreamingResponse", "kind": "Gdef"}, "Beta": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.Beta", "kind": "Gdef"}, "BetaWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.BetaWithRawResponse", "kind": "Gdef"}, "BetaWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.BetaWithStreamingResponse", "kind": "Gdef"}, "Chat": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.Chat", "kind": "Gdef"}, "ChatWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.ChatWithRawResponse", "kind": "Gdef"}, "ChatWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.chat.chat.ChatWithStreamingResponse", "kind": "Gdef"}, "Completions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.completions.Completions", "kind": "Gdef"}, "CompletionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.completions.CompletionsWithRawResponse", "kind": "Gdef"}, "CompletionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.completions.CompletionsWithStreamingResponse", "kind": "Gdef"}, "Containers": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.Containers", "kind": "Gdef"}, "ContainersWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.ContainersWithRawResponse", "kind": "Gdef"}, "ContainersWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.ContainersWithStreamingResponse", "kind": "Gdef"}, "Embeddings": {".class": "SymbolTableNode", "cross_ref": "openai.resources.embeddings.Embeddings", "kind": "Gdef"}, "EmbeddingsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.embeddings.EmbeddingsWithRawResponse", "kind": "Gdef"}, "EmbeddingsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.embeddings.EmbeddingsWithStreamingResponse", "kind": "Gdef"}, "Evals": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.Evals", "kind": "Gdef"}, "EvalsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.EvalsWithRawResponse", "kind": "Gdef"}, "EvalsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.evals.EvalsWithStreamingResponse", "kind": "Gdef"}, "Files": {".class": "SymbolTableNode", "cross_ref": "openai.resources.files.Files", "kind": "Gdef"}, "FilesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.files.FilesWithRawResponse", "kind": "Gdef"}, "FilesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.files.FilesWithStreamingResponse", "kind": "Gdef"}, "FineTuning": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.FineTuning", "kind": "Gdef"}, "FineTuningWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.FineTuningWithRawResponse", "kind": "Gdef"}, "FineTuningWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.fine_tuning.FineTuningWithStreamingResponse", "kind": "Gdef"}, "Images": {".class": "SymbolTableNode", "cross_ref": "openai.resources.images.Images", "kind": "Gdef"}, "ImagesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.images.ImagesWithRawResponse", "kind": "Gdef"}, "ImagesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.images.ImagesWithStreamingResponse", "kind": "Gdef"}, "Models": {".class": "SymbolTableNode", "cross_ref": "openai.resources.models.Models", "kind": "Gdef"}, "ModelsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.models.ModelsWithRawResponse", "kind": "Gdef"}, "ModelsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.models.ModelsWithStreamingResponse", "kind": "Gdef"}, "Moderations": {".class": "SymbolTableNode", "cross_ref": "openai.resources.moderations.Moderations", "kind": "Gdef"}, "ModerationsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.moderations.ModerationsWithRawResponse", "kind": "Gdef"}, "ModerationsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.moderations.ModerationsWithStreamingResponse", "kind": "Gdef"}, "Uploads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.Uploads", "kind": "Gdef"}, "UploadsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.UploadsWithRawResponse", "kind": "Gdef"}, "UploadsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.uploads.uploads.UploadsWithStreamingResponse", "kind": "Gdef"}, "VectorStores": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.VectorStores", "kind": "Gdef"}, "VectorStoresWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.VectorStoresWithRawResponse", "kind": "Gdef"}, "VectorStoresWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.vector_stores.vector_stores.VectorStoresWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\__init__.py"}