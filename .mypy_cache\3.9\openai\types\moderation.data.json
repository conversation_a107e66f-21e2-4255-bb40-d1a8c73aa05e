{".class": "MypyFile", "_fullname": "openai.types.moderation", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseModel": {".class": "SymbolTableNode", "cross_ref": "openai._models.BaseModel", "kind": "Gdef", "module_public": false}, "Categories": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.moderation.Categories", "name": "Categories", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.moderation.Categories", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 14, "name": "harassment", "type": "builtins.bool"}, {"alias": "harassment/threatening", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 20, "name": "harassment_threatening", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 26, "name": "hate", "type": "builtins.bool"}, {"alias": "hate/threatening", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 34, "name": "hate_threatening", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 41, "name": "illicit", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": "illicit/violent", "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 48, "name": "illicit_violent", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": "self-harm", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 55, "name": "self_harm", "type": "builtins.bool"}, {"alias": "self-harm/instructions", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 61, "name": "self_harm_instructions", "type": "builtins.bool"}, {"alias": "self-harm/intent", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 68, "name": "self_harm_intent", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 74, "name": "sexual", "type": "builtins.bool"}, {"alias": "sexual/minors", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 81, "name": "sexual_minors", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 84, "name": "violence", "type": "builtins.bool"}, {"alias": "violence/graphic", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 87, "name": "violence_graphic", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.moderation", "mro": ["openai.types.moderation.Categories", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.moderation.Categories.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 5, 5, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.moderation.Categories.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 5, 5, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "arg_types": ["openai.types.moderation.Categories", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Categories", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.moderation.Categories.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Categories", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.moderation.Categories.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Categories", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "harassment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.Categories.harassment", "name": "harassment", "setter_type": null, "type": "builtins.bool"}}, "harassment_threatening": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.Categories.harassment_threatening", "name": "harassment_threatening", "setter_type": null, "type": "builtins.bool"}}, "hate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.Categories.hate", "name": "hate", "setter_type": null, "type": "builtins.bool"}}, "hate_threatening": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.Categories.hate_threatening", "name": "hate_threatening", "setter_type": null, "type": "builtins.bool"}}, "illicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.Categories.illicit", "name": "illicit", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "illicit_violent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.Categories.illicit_violent", "name": "illicit_violent", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "self_harm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.Categories.self_harm", "name": "self_harm", "setter_type": null, "type": "builtins.bool"}}, "self_harm_instructions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.Categories.self_harm_instructions", "name": "self_harm_instructions", "setter_type": null, "type": "builtins.bool"}}, "self_harm_intent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.Categories.self_harm_intent", "name": "self_harm_intent", "setter_type": null, "type": "builtins.bool"}}, "sexual": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.Categories.sexual", "name": "sexual", "setter_type": null, "type": "builtins.bool"}}, "sexual_minors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.Categories.sexual_minors", "name": "sexual_minors", "setter_type": null, "type": "builtins.bool"}}, "violence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.Categories.violence", "name": "violence", "setter_type": null, "type": "builtins.bool"}}, "violence_graphic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.Categories.violence_graphic", "name": "violence_graphic", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.moderation.Categories.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.moderation.Categories", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CategoryAppliedInputTypes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.moderation.CategoryAppliedInputTypes", "name": "CategoryAppliedInputTypes", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.moderation.CategoryAppliedInputTypes", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 92, "name": "harassment", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": "harassment/threatening", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 95, "name": "harassment_threatening", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 98, "name": "hate", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": "hate/threatening", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 101, "name": "hate_threatening", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 104, "name": "illicit", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": "illicit/violent", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 107, "name": "illicit_violent", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": "self-harm", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 110, "name": "self_harm", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": "self-harm/instructions", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 113, "name": "self_harm_instructions", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": "self-harm/intent", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 116, "name": "self_harm_intent", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 119, "name": "sexual", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": "sexual/minors", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 122, "name": "sexual_minors", "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 125, "name": "violence", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": "violence/graphic", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 128, "name": "violence_graphic", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.moderation", "mro": ["openai.types.moderation.CategoryAppliedInputTypes", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "arg_types": ["openai.types.moderation.CategoryAppliedInputTypes", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CategoryAppliedInputTypes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CategoryAppliedInputTypes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CategoryAppliedInputTypes", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "harassment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.harassment", "name": "harassment", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "harassment_threatening": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.harassment_threatening", "name": "harassment_threatening", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "hate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.hate", "name": "hate", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "hate_threatening": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.hate_threatening", "name": "hate_threatening", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "illicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.illicit", "name": "illicit", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "illicit_violent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.illicit_violent", "name": "illicit_violent", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "self_harm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.self_harm", "name": "self_harm", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "self_harm_instructions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.self_harm_instructions", "name": "self_harm_instructions", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "self_harm_intent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.self_harm_intent", "name": "self_harm_intent", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "sexual": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.sexual", "name": "sexual", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "sexual_minors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.sexual_minors", "name": "sexual_minors", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "violence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.violence", "name": "violence", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "violence_graphic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryAppliedInputTypes.violence_graphic", "name": "violence_graphic", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "image"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.moderation.CategoryAppliedInputTypes.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.moderation.CategoryAppliedInputTypes", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "CategoryScores": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.moderation.CategoryScores", "name": "CategoryScores", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.moderation.CategoryScores", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 133, "name": "harassment", "type": "builtins.float"}, {"alias": "harassment/threatening", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 136, "name": "harassment_threatening", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 139, "name": "hate", "type": "builtins.float"}, {"alias": "hate/threatening", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 142, "name": "hate_threatening", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 145, "name": "illicit", "type": "builtins.float"}, {"alias": "illicit/violent", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 148, "name": "illicit_violent", "type": "builtins.float"}, {"alias": "self-harm", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 151, "name": "self_harm", "type": "builtins.float"}, {"alias": "self-harm/instructions", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 154, "name": "self_harm_instructions", "type": "builtins.float"}, {"alias": "self-harm/intent", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 157, "name": "self_harm_intent", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 160, "name": "sexual", "type": "builtins.float"}, {"alias": "sexual/minors", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 163, "name": "sexual_minors", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 166, "name": "violence", "type": "builtins.float"}, {"alias": "violence/graphic", "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 169, "name": "violence_graphic", "type": "builtins.float"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.moderation", "mro": ["openai.types.moderation.CategoryScores", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.moderation.CategoryScores.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.moderation.CategoryScores.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "arg_types": ["openai.types.moderation.CategoryScores", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of CategoryScores", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.moderation.CategoryScores.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CategoryScores", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.moderation.CategoryScores.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "harassment", "harassment/threatening", "hate", "hate/threatening", "illicit", "illicit/violent", "self-harm", "self-harm/instructions", "self-harm/intent", "sexual", "sexual/minors", "violence", "violence/graphic"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float", "builtins.float"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of CategoryScores", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "harassment": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.CategoryScores.harassment", "name": "harassment", "setter_type": null, "type": "builtins.float"}}, "harassment_threatening": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryScores.harassment_threatening", "name": "harassment_threatening", "setter_type": null, "type": "builtins.float"}}, "hate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.CategoryScores.hate", "name": "hate", "setter_type": null, "type": "builtins.float"}}, "hate_threatening": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryScores.hate_threatening", "name": "hate_threatening", "setter_type": null, "type": "builtins.float"}}, "illicit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.CategoryScores.illicit", "name": "illicit", "setter_type": null, "type": "builtins.float"}}, "illicit_violent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryScores.illicit_violent", "name": "illicit_violent", "setter_type": null, "type": "builtins.float"}}, "self_harm": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryScores.self_harm", "name": "self_harm", "setter_type": null, "type": "builtins.float"}}, "self_harm_instructions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryScores.self_harm_instructions", "name": "self_harm_instructions", "setter_type": null, "type": "builtins.float"}}, "self_harm_intent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryScores.self_harm_intent", "name": "self_harm_intent", "setter_type": null, "type": "builtins.float"}}, "sexual": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.CategoryScores.sexual", "name": "sexual", "setter_type": null, "type": "builtins.float"}}, "sexual_minors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryScores.sexual_minors", "name": "sexual_minors", "setter_type": null, "type": "builtins.float"}}, "violence": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.CategoryScores.violence", "name": "violence", "setter_type": null, "type": "builtins.float"}}, "violence_graphic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.moderation.CategoryScores.violence_graphic", "name": "violence_graphic", "setter_type": null, "type": "builtins.float"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.moderation.CategoryScores.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.moderation.CategoryScores", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FieldInfo": {".class": "SymbolTableNode", "cross_ref": "pydantic.fields.Field", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "Moderation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.moderation.Moderation", "name": "Moderation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.moderation.Moderation", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 174, "name": "categories", "type": "openai.types.moderation.Categories"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 177, "name": "category_applied_input_types", "type": "openai.types.moderation.CategoryAppliedInputTypes"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 182, "name": "category_scores", "type": "openai.types.moderation.CategoryScores"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 185, "name": "flagged", "type": "builtins.bool"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.moderation", "mro": ["openai.types.moderation.Moderation", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.moderation.Moderation.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "categories", "category_applied_input_types", "category_scores", "flagged"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.moderation.Moderation.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "categories", "category_applied_input_types", "category_scores", "flagged"], "arg_types": ["openai.types.moderation.Moderation", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "openai.types.moderation.Categories", "openai.types.moderation.CategoryAppliedInputTypes", "openai.types.moderation.CategoryScores", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Moderation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "categories", "category_applied_input_types", "category_scores", "flagged"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.moderation.Moderation.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "categories", "category_applied_input_types", "category_scores", "flagged"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "openai.types.moderation.Categories", "openai.types.moderation.CategoryAppliedInputTypes", "openai.types.moderation.CategoryScores", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Moderation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.moderation.Moderation.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "categories", "category_applied_input_types", "category_scores", "flagged"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "openai.types.moderation.Categories", "openai.types.moderation.CategoryAppliedInputTypes", "openai.types.moderation.CategoryScores", "builtins.bool"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Moderation", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "categories": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.Moderation.categories", "name": "categories", "setter_type": null, "type": "openai.types.moderation.Categories"}}, "category_applied_input_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.Moderation.category_applied_input_types", "name": "category_applied_input_types", "setter_type": null, "type": "openai.types.moderation.CategoryAppliedInputTypes"}}, "category_scores": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.Moderation.category_scores", "name": "category_scores", "setter_type": null, "type": "openai.types.moderation.CategoryScores"}}, "flagged": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.moderation.Moderation.flagged", "name": "flagged", "setter_type": null, "type": "builtins.bool"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.moderation.Moderation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.moderation.Moderation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.types.moderation.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.moderation.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.moderation.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.moderation.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.moderation.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.moderation.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.moderation.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\moderation.py"}