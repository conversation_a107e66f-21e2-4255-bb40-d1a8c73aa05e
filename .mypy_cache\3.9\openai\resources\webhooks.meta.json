{"data_mtime": 1754400649, "dep_lines": [17, 12, 13, 14, 15, 16, 3, 5, 6, 7, 8, 9, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.webhooks.unwrap_webhook_event", "openai._types", "openai._utils", "openai._models", "openai._resource", "openai._exceptions", "__future__", "hmac", "json", "time", "base64", "<PERSON><PERSON><PERSON>", "typing", "builtins", "_frozen_importlib", "abc", "openai.types", "openai.types.webhooks", "openai.types.webhooks.batch_cancelled_webhook_event", "openai.types.webhooks.batch_completed_webhook_event", "openai.types.webhooks.batch_expired_webhook_event", "openai.types.webhooks.batch_failed_webhook_event", "openai.types.webhooks.eval_run_canceled_webhook_event", "openai.types.webhooks.eval_run_failed_webhook_event", "openai.types.webhooks.eval_run_succeeded_webhook_event", "openai.types.webhooks.fine_tuning_job_cancelled_webhook_event", "openai.types.webhooks.fine_tuning_job_failed_webhook_event", "openai.types.webhooks.fine_tuning_job_succeeded_webhook_event", "openai.types.webhooks.response_cancelled_webhook_event", "openai.types.webhooks.response_completed_webhook_event", "openai.types.webhooks.response_failed_webhook_event", "openai.types.webhooks.response_incomplete_webhook_event", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "40a5d49474d5b8479804b2160b1b8723ef329a5c", "id": "openai.resources.webhooks", "ignore_all": true, "interface_hash": "764c9171df979681fb79736b4063c8d13c66160b", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\webhooks.py", "plugin_data": null, "size": 7820, "suppressed": [], "version_id": "1.17.1"}