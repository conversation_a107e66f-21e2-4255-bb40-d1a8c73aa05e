{"data_mtime": 1754400651, "dep_lines": [23, 40, 35, 30, 31, 32, 33, 34, 35, 20, 30, 17, 18, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 36, 37], "dep_prios": [5, 5, 10, 10, 10, 10, 10, 10, 20, 5, 20, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5], "dependencies": ["google.auth.aio.transport.sessions", "google.api_core.operations_v1.transports.base", "google.auth.aio.credentials", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.path_template", "google.api_core.rest_helpers", "google.api_core.retry_async", "google.auth.aio", "google.auth", "google.api_core", "json", "typing", "builtins", "_collections_abc", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.gapic_v1.method_async", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary_async", "google.auth._credentials_base", "google.auth.aio.transport", "google.auth.credentials"], "hash": "aedc31dc599a67e3ba287a78da70bc69eb50c640", "id": "google.api_core.operations_v1.transports.rest_asyncio", "ignore_all": true, "interface_hash": "124c6057d62b4194c565bfc2833d69fc2fd640de", "mtime": 1754241449, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\transports\\rest_asyncio.py", "plugin_data": null, "size": 24822, "suppressed": ["google.longrunning", "google.protobuf"], "version_id": "1.17.1"}