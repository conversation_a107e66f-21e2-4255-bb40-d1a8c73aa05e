{"data_mtime": 1754400654, "dep_lines": [19, 20, 21, 22, 16, 17, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["google.ai.generativelanguage_v1beta.services.model_service.transports.base", "google.ai.generativelanguage_v1beta.services.model_service.transports.grpc", "google.ai.generativelanguage_v1beta.services.model_service.transports.grpc_asyncio", "google.ai.generativelanguage_v1beta.services.model_service.transports.rest", "collections", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "google.ai.generativelanguage_v1beta.services.model_service.transports.rest_base", "google.api_core", "google.api_core.client_info", "google.api_core.gapic_v1", "google.api_core.gapic_v1.client_info", "google.auth", "google.auth._credentials_base", "google.auth.credentials"], "hash": "22da936b2a9c2e4f6a08097b5ebb2c86ff4bd54d", "id": "google.ai.generativelanguage_v1beta.services.model_service.transports", "ignore_all": true, "interface_hash": "f61c3ca5297ddec6a2a0485363f5d7390e440dfc", "mtime": 1754241453, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\model_service\\transports\\__init__.py", "plugin_data": null, "size": 1372, "suppressed": [], "version_id": "1.17.1"}