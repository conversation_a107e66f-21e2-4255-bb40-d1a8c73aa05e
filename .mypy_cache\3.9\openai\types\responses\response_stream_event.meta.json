{"data_mtime": 1754400650, "dep_lines": [7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 6, 3, 4, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30], "dependencies": ["openai.types.responses.response_error_event", "openai.types.responses.response_failed_event", "openai.types.responses.response_queued_event", "openai.types.responses.response_created_event", "openai.types.responses.response_completed_event", "openai.types.responses.response_text_done_event", "openai.types.responses.response_audio_done_event", "openai.types.responses.response_incomplete_event", "openai.types.responses.response_text_delta_event", "openai.types.responses.response_audio_delta_event", "openai.types.responses.response_in_progress_event", "openai.types.responses.response_refusal_done_event", "openai.types.responses.response_refusal_delta_event", "openai.types.responses.response_mcp_call_failed_event", "openai.types.responses.response_output_item_done_event", "openai.types.responses.response_content_part_done_event", "openai.types.responses.response_output_item_added_event", "openai.types.responses.response_content_part_added_event", "openai.types.responses.response_mcp_call_completed_event", "openai.types.responses.response_mcp_call_in_progress_event", "openai.types.responses.response_audio_transcript_done_event", "openai.types.responses.response_mcp_list_tools_failed_event", "openai.types.responses.response_audio_transcript_delta_event", "openai.types.responses.response_reasoning_summary_done_event", "openai.types.responses.response_mcp_call_arguments_done_event", "openai.types.responses.response_reasoning_summary_delta_event", "openai.types.responses.response_image_gen_call_completed_event", "openai.types.responses.response_mcp_call_arguments_delta_event", "openai.types.responses.response_mcp_list_tools_completed_event", "openai.types.responses.response_image_gen_call_generating_event", "openai.types.responses.response_web_search_call_completed_event", "openai.types.responses.response_web_search_call_searching_event", "openai.types.responses.response_file_search_call_completed_event", "openai.types.responses.response_file_search_call_searching_event", "openai.types.responses.response_image_gen_call_in_progress_event", "openai.types.responses.response_mcp_list_tools_in_progress_event", "openai.types.responses.response_reasoning_summary_part_done_event", "openai.types.responses.response_reasoning_summary_text_done_event", "openai.types.responses.response_web_search_call_in_progress_event", "openai.types.responses.response_file_search_call_in_progress_event", "openai.types.responses.response_function_call_arguments_done_event", "openai.types.responses.response_image_gen_call_partial_image_event", "openai.types.responses.response_output_text_annotation_added_event", "openai.types.responses.response_reasoning_summary_part_added_event", "openai.types.responses.response_reasoning_summary_text_delta_event", "openai.types.responses.response_function_call_arguments_delta_event", "openai.types.responses.response_code_interpreter_call_code_done_event", "openai.types.responses.response_code_interpreter_call_completed_event", "openai.types.responses.response_code_interpreter_call_code_delta_event", "openai.types.responses.response_code_interpreter_call_in_progress_event", "openai.types.responses.response_code_interpreter_call_interpreting_event", "openai._utils", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc"], "hash": "0d271774af5f7ada3d1dc47dc7653bed8547ce72", "id": "openai.types.responses.response_stream_event", "ignore_all": true, "interface_hash": "273f2e7b09457a102d37deda9035049c5707ad4a", "mtime": 1754241437, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_stream_event.py", "plugin_data": null, "size": 6653, "suppressed": [], "version_id": "1.17.1"}