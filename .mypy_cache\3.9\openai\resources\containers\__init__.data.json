{".class": "MypyFile", "_fullname": "openai.resources.containers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncContainers": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.AsyncContainers", "kind": "Gdef"}, "AsyncContainersWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.AsyncContainersWithRawResponse", "kind": "Gdef"}, "AsyncContainersWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.AsyncContainersWithStreamingResponse", "kind": "Gdef"}, "AsyncFiles": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.AsyncFiles", "kind": "Gdef"}, "AsyncFilesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.AsyncFilesWithRawResponse", "kind": "Gdef"}, "AsyncFilesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.AsyncFilesWithStreamingResponse", "kind": "Gdef"}, "Containers": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.Containers", "kind": "Gdef"}, "ContainersWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.ContainersWithRawResponse", "kind": "Gdef"}, "ContainersWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.containers.ContainersWithStreamingResponse", "kind": "Gdef"}, "Files": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.Files", "kind": "Gdef"}, "FilesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.FilesWithRawResponse", "kind": "Gdef"}, "FilesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.containers.files.files.FilesWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.containers.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.containers.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\containers\\__init__.py"}