{"data_mtime": 1754400653, "dep_lines": [29, 28, 29, 20, 21, 22, 24, 26, 28, 19, 23, 26, 16, 17, 19, 1, 1, 1, 1, 1, 1, 1, 1, 1, 25], "dep_prios": [10, 10, 20, 10, 10, 10, 10, 10, 20, 10, 10, 20, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 5], "dependencies": ["google.ai.generativelanguage_v1beta.types.text_service", "google.ai.generativelanguage_v1beta.gapic_version", "google.ai.generativelanguage_v1beta.types", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry", "google.auth.credentials", "google.oauth2.service_account", "google.ai.generativelanguage_v1beta", "google.api_core", "google.auth", "google.oauth2", "abc", "typing", "google", "builtins", "_frozen_importlib", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "google.auth._default"], "hash": "2daaa996f171ffc56a0d39f9638687fb0a38ee49", "id": "google.ai.generativelanguage_v1beta.services.text_service.transports.base", "ignore_all": true, "interface_hash": "2d8ccae1ecdaebd66c7f614b20ef9113b38ea112", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\base.py", "plugin_data": null, "size": 10077, "suppressed": ["google.longrunning"], "version_id": "1.17.1"}