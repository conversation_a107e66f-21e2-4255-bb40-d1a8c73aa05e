{".class": "MypyFile", "_fullname": "openai.resources.vector_stores.files", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.AsyncAPIResource", "kind": "Gdef", "module_public": false}, "AsyncCursorPage": {".class": "SymbolTableNode", "cross_ref": "openai.pagination.AsyncCursorPage", "kind": "Gdef", "module_public": false}, "AsyncFiles": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.AsyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.vector_stores.files.AsyncFiles", "name": "AsyncFiles", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.vector_stores.files.AsyncFiles", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.vector_stores.files", "mro": ["openai.resources.vector_stores.files.AsyncFiles", "openai._resource.AsyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.content", "name": "content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "content of AsyncFiles", "ret_type": {".class": "Instance", "args": ["openai.types.vector_stores.file_content_response.FileContentResponse", {".class": "Instance", "args": ["openai.types.vector_stores.file_content_response.FileContentResponse"], "extra_attrs": null, "type_ref": "openai.pagination.AsyncPage"}], "extra_attrs": null, "type_ref": "openai._base_client.AsyncPaginator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "vector_store_id", "file_id", "attributes", "chunking_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "vector_store_id", "file_id", "attributes", "chunking_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncFiles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_and_poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "attributes", "poll_interval_ms", "chunking_strategy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.create_and_poll", "name": "create_and_poll", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "attributes", "poll_interval_ms", "chunking_strategy"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_poll of AsyncFiles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of AsyncFiles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.vector_stores.vector_store_file_deleted.VectorStoreFileDeleted"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "vector_store_id", "after", "before", "filter", "limit", "order", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.list", "name": "list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "vector_store_id", "after", "before", "filter", "limit", "order", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list of AsyncFiles", "ret_type": {".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile", {".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai.pagination.AsyncCursorPage"}], "extra_attrs": null, "type_ref": "openai._base_client.AsyncPaginator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "file_id", "vector_store_id", "poll_interval_ms"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.poll", "name": "poll", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "file_id", "vector_store_id", "poll_interval_ms"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "poll of AsyncFiles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.retrieve", "name": "retrieve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retrieve of AsyncFiles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "attributes", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "attributes", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of AsyncFiles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5], "arg_names": ["self", "vector_store_id", "file", "chunking_strategy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.upload", "name": "upload", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5], "arg_names": ["self", "vector_store_id", "file", "chunking_strategy"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upload of AsyncFiles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upload_and_poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "vector_store_id", "file", "attributes", "poll_interval_ms", "chunking_strategy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.upload_and_poll", "name": "upload_and_poll", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "vector_store_id", "file", "attributes", "poll_interval_ms", "chunking_strategy"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upload_and_poll of AsyncFiles", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncFiles", "ret_type": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncFiles", "ret_type": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncFiles", "ret_type": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFiles.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.vector_stores.files.AsyncFiles"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncFiles", "ret_type": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.vector_stores.files.AsyncFiles.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.vector_stores.files.AsyncFiles", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFilesWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse", "name": "AsyncFilesWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.vector_stores.files", "mro": ["openai.resources.vector_stores.files.AsyncFilesWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "files"], "arg_types": ["openai.resources.vector_stores.files.AsyncFilesWithRawResponse", "openai.resources.vector_stores.files.AsyncFiles"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFilesWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse._files", "name": "_files", "setter_type": null, "type": "openai.resources.vector_stores.files.AsyncFiles"}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse.content", "name": "content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.file_content_response.FileContentResponse"], "extra_attrs": null, "type_ref": "openai.pagination.AsyncPage"}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["vector_store_id", "file_id", "attributes", "chunking_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file_deleted.VectorStoreFileDeleted"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse.list", "name": "list", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["vector_store_id", "after", "before", "filter", "limit", "order", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai.pagination.AsyncCursorPage"}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "attributes", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.vector_stores.files.AsyncFilesWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncFilesWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse", "name": "AsyncFilesWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.vector_stores.files", "mro": ["openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "files"], "arg_types": ["openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse", "openai.resources.vector_stores.files.AsyncFiles"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncFilesWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse._files", "name": "_files", "setter_type": null, "type": "openai.resources.vector_stores.files.AsyncFiles"}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse.content", "name": "content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.file_content_response.FileContentResponse"], "extra_attrs": null, "type_ref": "openai.pagination.AsyncPage"}], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["vector_store_id", "file_id", "attributes", "chunking_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file_deleted.VectorStoreFileDeleted"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse.list", "name": "list", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["vector_store_id", "after", "before", "filter", "limit", "order", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai.pagination.AsyncCursorPage"}], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "attributes", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.vector_stores.files.AsyncFilesWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncPage": {".class": "SymbolTableNode", "cross_ref": "openai.pagination.AsyncPage", "kind": "Gdef", "module_public": false}, "AsyncPaginator": {".class": "SymbolTableNode", "cross_ref": "openai._base_client.AsyncPaginator", "kind": "Gdef", "module_public": false}, "Body": {".class": "SymbolTableNode", "cross_ref": "openai._types.Body", "kind": "Gdef", "module_public": false}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "FileChunkingStrategyParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam", "kind": "Gdef", "module_public": false}, "FileContentResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.file_content_response.FileContentResponse", "kind": "Gdef", "module_public": false}, "FileTypes": {".class": "SymbolTableNode", "cross_ref": "openai._types.FileTypes", "kind": "Gdef", "module_public": false}, "Files": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.SyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.vector_stores.files.Files", "name": "Files", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.vector_stores.files.Files", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.vector_stores.files", "mro": ["openai.resources.vector_stores.files.Files", "openai._resource.SyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.content", "name": "content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.Files", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "content of Files", "ret_type": {".class": "Instance", "args": ["openai.types.vector_stores.file_content_response.FileContentResponse"], "extra_attrs": null, "type_ref": "openai.pagination.SyncPage"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "vector_store_id", "file_id", "attributes", "chunking_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "vector_store_id", "file_id", "attributes", "chunking_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.Files", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Files", "ret_type": "openai.types.vector_stores.vector_store_file.VectorStoreFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_and_poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "attributes", "poll_interval_ms", "chunking_strategy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.create_and_poll", "name": "create_and_poll", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "attributes", "poll_interval_ms", "chunking_strategy"], "arg_types": ["openai.resources.vector_stores.files.Files", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_and_poll of Files", "ret_type": "openai.types.vector_stores.vector_store_file.VectorStoreFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.Files", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of Files", "ret_type": "openai.types.vector_stores.vector_store_file_deleted.VectorStoreFileDeleted", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "vector_store_id", "after", "before", "filter", "limit", "order", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.list", "name": "list", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "vector_store_id", "after", "before", "filter", "limit", "order", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.Files", "builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list of Files", "ret_type": {".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai.pagination.SyncCursorPage"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "file_id", "vector_store_id", "poll_interval_ms"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.poll", "name": "poll", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": ["self", "file_id", "vector_store_id", "poll_interval_ms"], "arg_types": ["openai.resources.vector_stores.files.Files", "builtins.str", "builtins.str", {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "poll of Files", "ret_type": "openai.types.vector_stores.vector_store_file.VectorStoreFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.retrieve", "name": "retrieve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.Files", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retrieve of Files", "ret_type": "openai.types.vector_stores.vector_store_file.VectorStoreFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "attributes", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 5, 5, 5, 5], "arg_names": ["self", "file_id", "vector_store_id", "attributes", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.vector_stores.files.Files", "builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of Files", "ret_type": "openai.types.vector_stores.vector_store_file.VectorStoreFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upload": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5], "arg_names": ["self", "vector_store_id", "file", "chunking_strategy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.upload", "name": "upload", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5], "arg_names": ["self", "vector_store_id", "file", "chunking_strategy"], "arg_types": ["openai.resources.vector_stores.files.Files", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upload of Files", "ret_type": "openai.types.vector_stores.vector_store_file.VectorStoreFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "upload_and_poll": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "vector_store_id", "file", "attributes", "poll_interval_ms", "chunking_strategy"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.upload_and_poll", "name": "upload_and_poll", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5], "arg_names": ["self", "vector_store_id", "file", "attributes", "poll_interval_ms", "chunking_strategy"], "arg_types": ["openai.resources.vector_stores.files.Files", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "upload_and_poll of Files", "ret_type": "openai.types.vector_stores.vector_store_file.VectorStoreFile", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.vector_stores.files.Files"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Files", "ret_type": "openai.resources.vector_stores.files.FilesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.vector_stores.files.Files.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.vector_stores.files.Files"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Files", "ret_type": "openai.resources.vector_stores.files.FilesWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.vector_stores.files.Files.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.vector_stores.files.Files"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Files", "ret_type": "openai.resources.vector_stores.files.FilesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.vector_stores.files.Files.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.vector_stores.files.Files"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Files", "ret_type": "openai.resources.vector_stores.files.FilesWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.vector_stores.files.Files.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.vector_stores.files.Files", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FilesWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.vector_stores.files.FilesWithRawResponse", "name": "FilesWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.vector_stores.files.FilesWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.vector_stores.files", "mro": ["openai.resources.vector_stores.files.FilesWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.FilesWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "files"], "arg_types": ["openai.resources.vector_stores.files.FilesWithRawResponse", "openai.resources.vector_stores.files.Files"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FilesWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithRawResponse._files", "name": "_files", "setter_type": null, "type": "openai.resources.vector_stores.files.Files"}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithRawResponse.content", "name": "content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.file_content_response.FileContentResponse"], "extra_attrs": null, "type_ref": "openai.pagination.SyncPage"}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithRawResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["vector_store_id", "file_id", "attributes", "chunking_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithRawResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file_deleted.VectorStoreFileDeleted"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithRawResponse.list", "name": "list", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["vector_store_id", "after", "before", "filter", "limit", "order", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai.pagination.SyncCursorPage"}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithRawResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithRawResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "attributes", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.vector_stores.files.FilesWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.vector_stores.files.FilesWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "FilesWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.vector_stores.files.FilesWithStreamingResponse", "name": "FilesWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.vector_stores.files.FilesWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.vector_stores.files", "mro": ["openai.resources.vector_stores.files.FilesWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "files"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.vector_stores.files.FilesWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "files"], "arg_types": ["openai.resources.vector_stores.files.FilesWithStreamingResponse", "openai.resources.vector_stores.files.Files"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FilesWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_files": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithStreamingResponse._files", "name": "_files", "setter_type": null, "type": "openai.resources.vector_stores.files.Files"}}, "content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithStreamingResponse.content", "name": "content", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.file_content_response.FileContentResponse"], "extra_attrs": null, "type_ref": "openai.pagination.SyncPage"}], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithStreamingResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["vector_store_id", "file_id", "attributes", "chunking_strategy", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.file_chunking_strategy_param.FileChunkingStrategyParam"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithStreamingResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file_deleted.VectorStoreFileDeleted"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithStreamingResponse.list", "name": "list", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["vector_store_id", "after", "before", "filter", "limit", "order", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai.pagination.SyncCursorPage"}], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithStreamingResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.vector_stores.files.FilesWithStreamingResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5], "arg_names": ["file_id", "vector_store_id", "attributes", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.float", "builtins.bool"], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.vector_stores.vector_store_file.VectorStoreFile"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.vector_stores.files.FilesWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.vector_stores.files.FilesWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Headers": {".class": "SymbolTableNode", "cross_ref": "openai._types.Headers", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "NOT_GIVEN": {".class": "SymbolTableNode", "cross_ref": "openai._types.NOT_GIVEN", "kind": "Gdef", "module_public": false}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Query": {".class": "SymbolTableNode", "cross_ref": "openai._types.Query", "kind": "Gdef", "module_public": false}, "SyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.SyncAPIResource", "kind": "Gdef", "module_public": false}, "SyncCursorPage": {".class": "SymbolTableNode", "cross_ref": "openai.pagination.SyncCursorPage", "kind": "Gdef", "module_public": false}, "SyncPage": {".class": "SymbolTableNode", "cross_ref": "openai.pagination.SyncPage", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "VectorStoreFile": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.vector_store_file.VectorStoreFile", "kind": "Gdef", "module_public": false}, "VectorStoreFileDeleted": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.vector_store_file_deleted.VectorStoreFileDeleted", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.vector_stores.files.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.files.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.files.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.files.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.files.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.files.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.vector_stores.files.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_legacy_response": {".class": "SymbolTableNode", "cross_ref": "openai._legacy_response", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.assert_never", "kind": "Gdef", "module_public": false}, "async_maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.async_maybe_transform", "kind": "Gdef", "module_public": false}, "async_to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.async_to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "openai._compat.cached_property", "kind": "Gdef", "module_public": false}, "file_create_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.file_create_params", "kind": "Gdef", "module_public": false}, "file_list_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.file_list_params", "kind": "Gdef", "module_public": false}, "file_update_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.vector_stores.file_update_params", "kind": "Gdef", "module_public": false}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef", "module_public": false}, "is_given": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_given", "kind": "Gdef", "module_public": false}, "make_request_options": {".class": "SymbolTableNode", "cross_ref": "openai._base_client.make_request_options", "kind": "Gdef", "module_public": false}, "maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.maybe_transform", "kind": "Gdef", "module_public": false}, "to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\vector_stores\\files.py"}