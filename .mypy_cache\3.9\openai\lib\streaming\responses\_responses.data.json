{".class": "MypyFile", "_fullname": "openai.lib.streaming.responses._responses", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "AsyncResponseStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream", "name": "AsyncResponseStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming.responses._responses", "mro": ["openai.lib.streaming.responses._responses.AsyncResponseStream", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}, "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aenter__ of AsyncResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}, "values": [], "variance": 0}]}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aexit__ of AsyncResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aiter__ of AsyncResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.responses._events.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__anext__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.__anext__", "name": "__anext__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__anext__ of AsyncResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.responses._events.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3], "arg_names": ["self", "raw_stream", "text_format", "input_tools", "starting_after"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3], "arg_names": ["self", "raw_stream", "text_format", "input_tools", "starting_after"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.ToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncResponseStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__stream__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.__stream__", "name": "__stream__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__stream__ of AsyncResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.responses._events.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iterator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream._iterator", "name": "_iterator", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.responses._events.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}}}, "_raw_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream._raw_stream", "name": "_raw_stream", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream._response", "name": "_response", "setter_type": null, "type": "httpx._models.Response"}}, "_starting_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream._starting_after", "name": "_starting_after", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream._state", "name": "_state", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStreamState"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of AsyncResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_final_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.get_final_response", "name": "get_final_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_final_response of AsyncResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "until_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.until_done", "name": "until_done", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}, "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "until_done of AsyncResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["TextFormatT"], "typeddict_type": null}}, "AsyncResponseStreamManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "name": "AsyncResponseStreamManager", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming.responses._responses", "mro": ["openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aenter__ of AsyncResponseStreamManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aexit__ of AsyncResponseStreamManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__api_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager.__api_request", "name": "__api_request", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["self", "api_request", "text_format", "input_tools", "starting_after"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["self", "api_request", "text_format", "input_tools", "starting_after"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.ToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncResponseStreamManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__input_tools": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager.__input_tools", "name": "__input_tools", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.ToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}}}, "__starting_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager.__starting_after", "name": "__starting_after", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager.__stream", "name": "__stream", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStream"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__text_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager.__text_format", "name": "__text_format", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "openai._types.NotGiven"], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.AsyncResponseStreamManager"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["TextFormatT"], "typeddict_type": null}}, "AsyncStream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.AsyncStream", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NOT_GIVEN": {".class": "SymbolTableNode", "cross_ref": "openai._types.NOT_GIVEN", "kind": "Gdef"}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef"}, "ParsedContent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedContent", "kind": "Gdef"}, "ParsedResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponse", "kind": "Gdef"}, "ParsedResponseFunctionToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall", "kind": "Gdef"}, "ParsedResponseOutputMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "kind": "Gdef"}, "ParsedResponseSnapshot": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._types.ParsedResponseSnapshot", "kind": "Gdef"}, "RawResponseStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent", "kind": "Gdef"}, "ResponseCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._events.ResponseCompletedEvent", "kind": "Gdef"}, "ResponseFunctionCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._events.ResponseFunctionCallArgumentsDeltaEvent", "kind": "Gdef"}, "ResponseStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.responses._responses.ResponseStream", "name": "ResponseStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._responses.ResponseStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming.responses._responses", "mro": ["openai.lib.streaming.responses._responses.ResponseStream", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._responses.ResponseStream.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.ResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}, "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of ResponseStream", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.ResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.ResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}, "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of ResponseStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 3], "arg_names": ["self", "raw_stream", "text_format", "input_tools", "starting_after"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 3], "arg_names": ["self", "raw_stream", "text_format", "input_tools", "starting_after"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.ToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResponseStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of ResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.responses._events.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__next__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__next__ of ResponseStream", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.responses._events.ResponseStreamEvent"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__stream__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream.__stream__", "name": "__stream__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__stream__ of ResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.responses._events.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iterator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream._iterator", "name": "_iterator", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.responses._events.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Iterator"}}}, "_raw_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream._raw_stream", "name": "_raw_stream", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream._response", "name": "_response", "setter_type": null, "type": "httpx._models.Response"}}, "_starting_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream._starting_after", "name": "_starting_after", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream._state", "name": "_state", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStreamState"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of ResponseStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_final_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStream.get_final_response", "name": "get_final_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_final_response of ResponseStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "until_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._responses.ResponseStream.until_done", "name": "until_done", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.ResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}, "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "until_done of ResponseStream", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.ResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.ResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.ResponseStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["TextFormatT"], "typeddict_type": null}}, "ResponseStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._events.ResponseStreamEvent", "kind": "Gdef"}, "ResponseStreamManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.responses._responses.ResponseStreamManager", "name": "ResponseStreamManager", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming.responses._responses", "mro": ["openai.lib.streaming.responses._responses.ResponseStreamManager", "builtins.object"], "names": {".class": "SymbolTable", "__api_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamManager.__api_request", "name": "__api_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamManager.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStreamManager"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of ResponseStreamManager", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamManager.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStreamManager"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of ResponseStreamManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["self", "api_request", "text_format", "input_tools", "starting_after"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3, 3], "arg_names": ["self", "api_request", "text_format", "input_tools", "starting_after"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStreamManager"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.ToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResponseStreamManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__input_tools": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamManager.__input_tools", "name": "__input_tools", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.ToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}}}, "__starting_after": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamManager.__starting_after", "name": "__starting_after", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamManager.__stream", "name": "__stream", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStream"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__text_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamManager.__text_format", "name": "__text_format", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "openai._types.NotGiven"], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.ResponseStreamManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStreamManager"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["TextFormatT"], "typeddict_type": null}}, "ResponseStreamState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState", "name": "ResponseStreamState", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming.responses._responses", "mro": ["openai.lib.streaming.responses._responses.ResponseStreamState", "builtins.object"], "names": {".class": "SymbolTable", "__current_snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState.__current_snapshot", "name": "__current_snapshot", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.responses._types.ParsedResponseSnapshot"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "input_tools", "text_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "input_tools", "text_format"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStreamState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.ToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResponseStreamState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_completed_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState._completed_response", "name": "_completed_response", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_create_initial_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState._create_initial_response", "name": "_create_initial_response", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStreamState"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_create_initial_response of ResponseStreamState", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.responses._types.ParsedResponseSnapshot"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_input_tools": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState._input_tools", "name": "_input_tools", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.function_tool_param.FunctionToolParam"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.file_search_tool_param.FileSearchToolParam"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.web_search_tool_param.WebSearchToolParam"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.computer_tool_param.ComputerToolParam"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.Mcp"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.CodeInterpreter"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.ImageGeneration"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.LocalShell"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_rich_text_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState._rich_text_format", "name": "_rich_text_format", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.type", "openai._types.NotGiven"], "uses_pep604_syntax": true}}}, "_text_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState._text_format", "name": "_text_format", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "openai._types.NotGiven"], "uses_pep604_syntax": true}}}, "accumulate_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState.accumulate_event", "name": "accumulate_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStreamState"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "accumulate_event of ResponseStreamState", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.responses._types.ParsedResponseSnapshot"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState.handle_event", "name": "handle_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStreamState"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_event of ResponseStreamState", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.responses._events.ResponseStreamEvent"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.responses._responses.ResponseStreamState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": 1, "name": "TextFormatT", "namespace": "openai.lib.streaming.responses._responses.ResponseStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.responses._responses.ResponseStreamState"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["TextFormatT"], "typeddict_type": null}}, "ResponseTextDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._events.ResponseTextDeltaEvent", "kind": "Gdef"}, "ResponseTextDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.responses._events.ResponseTextDoneEvent", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef"}, "Stream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.Stream", "kind": "Gdef"}, "TextFormatT": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._responses.TextFormatT", "kind": "Gdef"}, "ToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.tool_param.ToolParam", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._responses.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._responses.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._responses.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._responses.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._responses.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.responses._responses.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "build": {".class": "SymbolTableNode", "cross_ref": "openai._models.build", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "construct_type_unchecked": {".class": "SymbolTableNode", "cross_ref": "openai._models.construct_type_unchecked", "kind": "Gdef"}, "consume_async_iterator": {".class": "SymbolTableNode", "cross_ref": "openai._utils._streams.consume_async_iterator", "kind": "Gdef"}, "consume_sync_iterator": {".class": "SymbolTableNode", "cross_ref": "openai._utils._streams.consume_sync_iterator", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_given": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_given", "kind": "Gdef"}, "parse_response": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._responses.parse_response", "kind": "Gdef"}, "parse_text": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._responses.parse_text", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_responses.py"}