{".class": "MypyFile", "_fullname": "openai.lib.streaming._assistants", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AssistantEventHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming._assistants", "mro": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins.object"], "names": {".class": "SymbolTable", "__current_message_snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.__current_message_snapshot", "name": "__current_message_snapshot", "setter_type": null, "type": {".class": "UnionType", "items": ["openai.types.beta.threads.message.Message", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__current_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.__current_run", "name": "__current_run", "setter_type": null, "type": {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__current_run_step_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.__current_run_step_id", "name": "__current_run_step_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of AssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__message_snapshots": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.__message_snapshots", "name": "__message_snapshots", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "openai.types.beta.threads.message.Message"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__next__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__next__ of AssistantEventHand<PERSON>", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__run_step_snapshots": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.__run_step_snapshots", "name": "__run_step_snapshots", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "openai.types.beta.threads.runs.run_step.RunStep"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.__stream", "name": "__stream", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__stream__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.__stream__", "name": "__stream__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__stream__ of AssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__text_deltas__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.__text_deltas__", "name": "__text_deltas__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__text_deltas__ of AssistantEventHandler", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_current_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler._current_event", "name": "_current_event", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_current_message_content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler._current_message_content", "name": "_current_message_content", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.message_content.MessageContent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_current_message_content_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler._current_message_content_index", "name": "_current_message_content_index", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_current_tool_call": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler._current_tool_call", "name": "_current_tool_call", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.runs.tool_call.ToolCall"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_current_tool_call_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler._current_tool_call_index", "name": "_current_tool_call_index", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_emit_sse_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler._emit_sse_event", "name": "_emit_sse_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_emit_sse_event of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler._init", "name": "_init", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_init of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iterator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler._iterator", "name": "_iterator", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Iterator"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "current_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.current_event", "name": "current_event", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_event of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.current_event", "name": "current_event", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_event of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "current_message_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.current_message_snapshot", "name": "current_message_snapshot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_message_snapshot of AssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.message.Message", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.current_message_snapshot", "name": "current_message_snapshot", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_message_snapshot of AssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.message.Message", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "current_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.current_run", "name": "current_run", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_run of AssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.current_run", "name": "current_run", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_run of AssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "current_run_step_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.current_run_step_snapshot", "name": "current_run_step_snapshot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_run_step_snapshot of AssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.runs.run_step.RunStep", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.current_run_step_snapshot", "name": "current_run_step_snapshot", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_run_step_snapshot of AssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.runs.run_step.RunStep", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_final_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.get_final_messages", "name": "get_final_messages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_final_messages of AssistantEvent<PERSON><PERSON>ler", "ret_type": {".class": "Instance", "args": ["openai.types.beta.threads.message.Message"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_final_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.get_final_run", "name": "get_final_run", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_final_run of Assistant<PERSON><PERSON>H<PERSON><PERSON>", "ret_type": "openai.types.beta.threads.run.Run", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_final_run_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.get_final_run_steps", "name": "get_final_run_steps", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_final_run_steps of Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "Instance", "args": ["openai.types.beta.threads.runs.run_step.RunStep"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_end", "name": "on_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_end of Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_event", "name": "on_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_event of Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_exception", "name": "on_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "builtins.Exception"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_exception of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_image_file_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "image_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_image_file_done", "name": "on_image_file_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "image_file"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "openai.types.beta.threads.image_file.ImageFile"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_image_file_done of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_message_created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_message_created", "name": "on_message_created", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "openai.types.beta.threads.message.Message"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_message_created of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_message_delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_message_delta", "name": "on_message_delta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "openai.types.beta.threads.message_delta.MessageDelta", "openai.types.beta.threads.message.Message"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_message_delta of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_message_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_message_done", "name": "on_message_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "openai.types.beta.threads.message.Message"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_message_done of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_run_step_created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run_step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_run_step_created", "name": "on_run_step_created", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run_step"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "openai.types.beta.threads.runs.run_step.RunStep"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_run_step_created of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_run_step_delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_run_step_delta", "name": "on_run_step_delta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "openai.types.beta.threads.runs.run_step_delta.RunStepDelta", "openai.types.beta.threads.runs.run_step.RunStep"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_run_step_delta of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_run_step_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run_step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_run_step_done", "name": "on_run_step_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run_step"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "openai.types.beta.threads.runs.run_step.RunStep"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_run_step_done of AssistantEventH<PERSON>ler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_text_created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_text_created", "name": "on_text_created", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "openai.types.beta.threads.text.Text"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_text_created of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_text_delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_text_delta", "name": "on_text_delta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "openai.types.beta.threads.text_delta.TextDelta", "openai.types.beta.threads.text.Text"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_text_delta of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_text_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_text_done", "name": "on_text_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "openai.types.beta.threads.text.Text"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_text_done of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_timeout", "name": "on_timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_timeout of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_tool_call_created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tool_call"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_tool_call_created", "name": "on_tool_call_created", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tool_call"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.runs.tool_call.ToolCall"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_tool_call_created of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_tool_call_delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_tool_call_delta", "name": "on_tool_call_delta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.runs.tool_call_delta.ToolCallDelta"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.runs.tool_call.ToolCall"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_tool_call_delta of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_tool_call_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tool_call"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.on_tool_call_done", "name": "on_tool_call_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tool_call"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.runs.tool_call.ToolCall"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_tool_call_done of AssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "text_deltas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.text_deltas", "name": "text_deltas", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}}}, "until_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.until_done", "name": "until_done", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "until_done of <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AssistantEventHandlerT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}}, "AssistantStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent", "kind": "Gdef"}, "AssistantStreamManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming._assistants.AssistantStreamManager", "name": "Assistant<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": 1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.lib.streaming._assistants.AssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming._assistants.AssistantStreamManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming._assistants", "mro": ["openai.lib.streaming._assistants.AssistantStreamManager", "builtins.object"], "names": {".class": "SymbolTable", "__api_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantStreamManager.__api_request", "name": "__api_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantStreamManager.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": 1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.lib.streaming._assistants.AssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of AssistantStreamManager", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": 1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.lib.streaming._assistants.AssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__event_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantStreamManager.__event_handler", "name": "__event_handler", "setter_type": null, "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": 1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.lib.streaming._assistants.AssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantStreamManager.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": 1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.lib.streaming._assistants.AssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of AssistantStreamManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["self", "api_request", "event_handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AssistantStreamManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", "api_request", "event_handler"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": 1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.lib.streaming._assistants.AssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": 1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.lib.streaming._assistants.AssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AssistantStreamManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AssistantStreamManager.__stream", "name": "__stream", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantStreamManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AssistantEventHandlerT", "id": 1, "name": "AssistantEvent<PERSON><PERSON>ler<PERSON>", "namespace": "openai.lib.streaming._assistants.AssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.Assistant<PERSON><PERSON><PERSON><PERSON><PERSON>", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AssistantStreamManager"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["AssistantEvent<PERSON><PERSON>ler<PERSON>"], "typeddict_type": null}}, "AsyncAssistantEventHandler": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "name": "AsyncAssistantEventHandler", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming._assistants", "mro": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aiter__ of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__anext__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.__anext__", "name": "__anext__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__anext__ of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__current_message_snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.__current_message_snapshot", "name": "__current_message_snapshot", "setter_type": null, "type": {".class": "UnionType", "items": ["openai.types.beta.threads.message.Message", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__current_run": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.__current_run", "name": "__current_run", "setter_type": null, "type": {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__current_run_step_id": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.__current_run_step_id", "name": "__current_run_step_id", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncAssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__message_snapshots": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.__message_snapshots", "name": "__message_snapshots", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "openai.types.beta.threads.message.Message"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__run_step_snapshots": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.__run_step_snapshots", "name": "__run_step_snapshots", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", "openai.types.beta.threads.runs.run_step.RunStep"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.__stream", "name": "__stream", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__stream__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.__stream__", "name": "__stream__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__stream__ of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__text_deltas__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.__text_deltas__", "name": "__text_deltas__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__text_deltas__ of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_current_event": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler._current_event", "name": "_current_event", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_current_message_content": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler._current_message_content", "name": "_current_message_content", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.message_content.MessageContent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_current_message_content_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler._current_message_content_index", "name": "_current_message_content_index", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_current_tool_call": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler._current_tool_call", "name": "_current_tool_call", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.runs.tool_call.ToolCall"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_current_tool_call_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler._current_tool_call_index", "name": "_current_tool_call_index", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_emit_sse_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler._emit_sse_event", "name": "_emit_sse_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_emit_sse_event of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler._init", "name": "_init", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "stream"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_init of AsyncAssistantEventHandler", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iterator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler._iterator", "name": "_iterator", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "current_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.current_event", "name": "current_event", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_event of AsyncAssistantEventHandler", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.current_event", "name": "current_event", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_event of AsyncAssistantEventHandler", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "current_message_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.current_message_snapshot", "name": "current_message_snapshot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_message_snapshot of AsyncAssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.message.Message", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.current_message_snapshot", "name": "current_message_snapshot", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_message_snapshot of AsyncAssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.message.Message", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "current_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.current_run", "name": "current_run", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_run of AsyncAssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.current_run", "name": "current_run", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_run of AsyncAssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.run.Run", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "current_run_step_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.current_run_step_snapshot", "name": "current_run_step_snapshot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_run_step_snapshot of AsyncAssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.runs.run_step.RunStep", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.current_run_step_snapshot", "name": "current_run_step_snapshot", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_run_step_snapshot of AsyncAssistantEventHandler", "ret_type": {".class": "UnionType", "items": ["openai.types.beta.threads.runs.run_step.RunStep", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_final_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.get_final_messages", "name": "get_final_messages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_final_messages of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["openai.types.beta.threads.message.Message"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_final_run": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.get_final_run", "name": "get_final_run", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_final_run of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.beta.threads.run.Run"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_final_run_steps": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.get_final_run_steps", "name": "get_final_run_steps", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_final_run_steps of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["openai.types.beta.threads.runs.run_step.RunStep"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_end": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_end", "name": "on_end", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_end of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_event", "name": "on_event", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "event"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_event of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_exception": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_exception", "name": "on_exception", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "builtins.Exception"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_exception of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_image_file_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "image_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_image_file_done", "name": "on_image_file_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "image_file"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "openai.types.beta.threads.image_file.ImageFile"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_image_file_done of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_message_created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_message_created", "name": "on_message_created", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "openai.types.beta.threads.message.Message"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_message_created of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_message_delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_message_delta", "name": "on_message_delta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "openai.types.beta.threads.message_delta.MessageDelta", "openai.types.beta.threads.message.Message"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_message_delta of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_message_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "message"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_message_done", "name": "on_message_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "message"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "openai.types.beta.threads.message.Message"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_message_done of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_run_step_created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run_step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_run_step_created", "name": "on_run_step_created", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run_step"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "openai.types.beta.threads.runs.run_step.RunStep"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_run_step_created of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_run_step_delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_run_step_delta", "name": "on_run_step_delta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "openai.types.beta.threads.runs.run_step_delta.RunStepDelta", "openai.types.beta.threads.runs.run_step.RunStep"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_run_step_delta of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_run_step_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "run_step"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_run_step_done", "name": "on_run_step_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "run_step"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "openai.types.beta.threads.runs.run_step.RunStep"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_run_step_done of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_text_created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_text_created", "name": "on_text_created", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "openai.types.beta.threads.text.Text"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_text_created of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_text_delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_text_delta", "name": "on_text_delta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "openai.types.beta.threads.text_delta.TextDelta", "openai.types.beta.threads.text.Text"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_text_delta of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_text_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_text_done", "name": "on_text_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", "openai.types.beta.threads.text.Text"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_text_done of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_timeout": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_timeout", "name": "on_timeout", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_timeout of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_tool_call_created": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tool_call"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_tool_call_created", "name": "on_tool_call_created", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tool_call"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.runs.tool_call.ToolCall"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_tool_call_created of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_tool_call_delta": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_tool_call_delta", "name": "on_tool_call_delta", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "delta", "snapshot"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.runs.tool_call_delta.ToolCallDelta"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.runs.tool_call.ToolCall"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_tool_call_delta of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "on_tool_call_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "tool_call"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.on_tool_call_done", "name": "on_tool_call_done", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "tool_call"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.runs.tool_call.ToolCall"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "on_tool_call_done of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "text_deltas": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.text_deltas", "name": "text_deltas", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.AsyncIterable"}}}, "until_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.until_done", "name": "until_done", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib.streaming._assistants.AsyncAssistantEventHandler"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "until_done of AsyncAssistantEventHandler", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandler.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncAssistantEventHandlerT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "name": "AsyncAssistantEventHandlerT", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}}, "AsyncAssistantStreamManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "name": "AsyncAssistantStreamManager", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": 1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming._assistants", "mro": ["openai.lib.streaming._assistants.AsyncAssistantStreamManager", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantStreamManager.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": 1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aenter__ of AsyncAssistantStreamManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": 1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantStreamManager.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": 1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aexit__ of AsyncAssistantStreamManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__api_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantStreamManager.__api_request", "name": "__api_request", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}}}, "__event_handler": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantStreamManager.__event_handler", "name": "__event_handler", "setter_type": null, "type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": 1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3], "arg_names": ["self", "api_request", "event_handler"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantStreamManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3], "arg_names": ["self", "api_request", "event_handler"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": 1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}, {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": 1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncAssistantStreamManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming._assistants.AsyncAssistantStreamManager.__stream", "name": "__stream", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantStreamManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming._assistants.AsyncAssistantEventHandlerT", "id": 1, "name": "AsyncAssistantEventHandlerT", "namespace": "openai.lib.streaming._assistants.AsyncAssistantStreamManager", "upper_bound": "openai.lib.streaming._assistants.AsyncAssistantEventHandler", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming._assistants.AsyncAssistantStreamManager"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["AsyncAssistantEventHandlerT"], "typeddict_type": null}}, "AsyncIterable": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterable", "kind": "Gdef"}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "AsyncStream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.AsyncStream", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "ImageFile": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.image_file.ImageFile", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "Message": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message.Message", "kind": "Gdef"}, "MessageContent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_content.MessageContent", "kind": "Gdef"}, "MessageContentDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_content_delta.MessageContentDelta", "kind": "Gdef"}, "MessageDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.message_delta.MessageDelta", "kind": "Gdef"}, "Run": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.run.Run", "kind": "Gdef"}, "RunStep": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.run_step.RunStep", "kind": "Gdef"}, "RunStepDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.run_step_delta.RunStepDelta", "kind": "Gdef"}, "Stream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.Stream", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Text": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.text.Text", "kind": "Gdef"}, "TextDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.text_delta.TextDelta", "kind": "Gdef"}, "ToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.tool_call.ToolCall", "kind": "Gdef"}, "ToolCallDelta": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.threads.runs.tool_call_delta.ToolCallDelta", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming._assistants.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming._assistants.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming._assistants.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming._assistants.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming._assistants.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming._assistants.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "accumulate_delta": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["acc", "delta"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming._assistants.accumulate_delta", "name": "accumulate_delta", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["acc", "delta"], "arg_types": [{".class": "Instance", "args": ["builtins.object", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.object", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "accumulate_delta", "ret_type": {".class": "Instance", "args": ["builtins.object", "builtins.object"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accumulate_event": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["event", "current_message_snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming._assistants.accumulate_event", "name": "accumulate_event", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["event", "current_message_snapshot"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}, {".class": "UnionType", "items": ["openai.types.beta.threads.message.Message", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "accumulate_event", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["openai.types.beta.threads.message.Message", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.threads.message_content_delta.MessageContentDelta"}], "extra_attrs": null, "type_ref": "builtins.list"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accumulate_run_step": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["event", "run_step_snapshots"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming._assistants.accumulate_run_step", "name": "accumulate_run_step", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["event", "run_step_snapshots"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.beta.assistant_stream_event.AssistantStreamEvent"}, {".class": "Instance", "args": ["builtins.str", "openai.types.beta.threads.runs.run_step.RunStep"], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "accumulate_run_step", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.assert_never", "kind": "Gdef"}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "construct_type": {".class": "SymbolTableNode", "cross_ref": "openai._models.construct_type", "kind": "Gdef"}, "consume_async_iterator": {".class": "SymbolTableNode", "cross_ref": "openai._utils._streams.consume_async_iterator", "kind": "Gdef"}, "consume_sync_iterator": {".class": "SymbolTableNode", "cross_ref": "openai._utils._streams.consume_sync_iterator", "kind": "Gdef"}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef"}, "is_dict": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_dict", "kind": "Gdef"}, "is_list": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_list", "kind": "Gdef"}, "model_dump": {".class": "SymbolTableNode", "cross_ref": "openai._compat.model_dump", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\_assistants.py"}