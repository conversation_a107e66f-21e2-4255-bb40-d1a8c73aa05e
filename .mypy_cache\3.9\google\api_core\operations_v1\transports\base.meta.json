{"data_mtime": **********, "dep_lines": [21, 22, 23, 24, 26, 28, 20, 25, 28, 16, 17, 18, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27, 29, 31], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 20, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry", "google.api_core.version", "google.auth.credentials", "google.oauth2.service_account", "google.api_core", "google.auth", "google.oauth2", "abc", "re", "typing", "google", "builtins", "_frozen_importlib", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "google.auth._default", "typing_extensions"], "hash": "e86808b0363226cdfe7435a6e66b6c46d4a7a649", "id": "google.api_core.operations_v1.transports.base", "ignore_all": true, "interface_hash": "6c2f091f9fc269d72f1329b69601abaf272d6f8c", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\transports\\base.py", "plugin_data": null, "size": 11419, "suppressed": ["google.longrunning", "google.protobuf", "grpc"], "version_id": "1.17.1"}