{".class": "MypyFile", "_fullname": "utils", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "utils.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "calculate_file_hash": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.calculate_file_hash", "name": "calculate_file_hash", "type": null}}, "create_virtual_environment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["venv_path", "python_cmd"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.create_virtual_environment", "name": "create_virtual_environment", "type": null}}, "get_executable_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1], "arg_names": ["base_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.get_executable_name", "name": "get_executable_name", "type": null}}, "get_pip_executable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["venv_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.get_pip_executable", "name": "get_pip_executable", "type": null}}, "get_project_root": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.get_project_root", "name": "get_project_root", "type": null}}, "get_python_executable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["venv_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.get_python_executable", "name": "get_python_executable", "type": null}}, "hashlib": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON>", "kind": "Gdef"}, "install_dependencies": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["venv_path", "requirements_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.install_dependencies", "name": "install_dependencies", "type": null}}, "kill_existing_exe_process": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["process_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.kill_existing_exe_process", "name": "kill_existing_exe_process", "type": null}}, "kill_python_script_process": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["script_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.kill_python_script_process", "name": "kill_python_script_process", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "python_exe_version": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.python_exe_version", "name": "python_exe_version", "type": null}}, "setup_environment": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["venv_path", "requirements_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.setup_environment", "name": "setup_environment", "type": null}}, "shutil": {".class": "SymbolTableNode", "cross_ref": "shutil", "kind": "Gdef"}, "subprocess": {".class": "SymbolTableNode", "cross_ref": "subprocess", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "terminate_existing_processes": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 1], "arg_names": ["exe_name", "script_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.terminate_existing_processes", "name": "terminate_existing_processes", "type": null}}, "verify_requirements": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["required_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "utils.verify_requirements", "name": "verify_requirements", "type": null}}}, "path": "C:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\scripts\\utils.py"}