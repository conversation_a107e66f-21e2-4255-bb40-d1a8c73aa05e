{".class": "MypyFile", "_fullname": "openai.types.beta.realtime.realtime_server_event", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef", "module_public": false}, "BaseModel": {".class": "SymbolTableNode", "cross_ref": "openai._models.BaseModel", "kind": "Gdef", "module_public": false}, "ConversationCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_created_event.ConversationCreatedEvent", "kind": "Gdef", "module_public": false}, "ConversationItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item.ConversationItem", "kind": "Gdef", "module_public": false}, "ConversationItemCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_created_event.ConversationItemCreatedEvent", "kind": "Gdef", "module_public": false}, "ConversationItemDeletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_deleted_event.ConversationItemDeletedEvent", "kind": "Gdef", "module_public": false}, "ConversationItemInputAudioTranscriptionCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_input_audio_transcription_completed_event.ConversationItemInputAudioTranscriptionCompletedEvent", "kind": "Gdef", "module_public": false}, "ConversationItemInputAudioTranscriptionDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_input_audio_transcription_delta_event.ConversationItemInputAudioTranscriptionDeltaEvent", "kind": "Gdef", "module_public": false}, "ConversationItemInputAudioTranscriptionFailedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_input_audio_transcription_failed_event.ConversationItemInputAudioTranscriptionFailedEvent", "kind": "Gdef", "module_public": false}, "ConversationItemRetrieved": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved", "name": "ConversationItemRetrieved", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 52, "name": "event_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 55, "name": "item", "type": "openai.types.beta.realtime.conversation_item.ConversationItem"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 58, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "conversation.item.retrieved"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.beta.realtime.realtime_server_event", "mro": ["openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3], "arg_names": ["self", "_request_id", "event_id", "item", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3], "arg_names": ["self", "_request_id", "event_id", "item", "type"], "arg_types": ["openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "openai.types.beta.realtime.conversation_item.ConversationItem", {".class": "LiteralType", "fallback": "builtins.str", "value": "conversation.item.retrieved"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ConversationItemRetrieved", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "item", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "item", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "openai.types.beta.realtime.conversation_item.ConversationItem", {".class": "LiteralType", "fallback": "builtins.str", "value": "conversation.item.retrieved"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ConversationItemRetrieved", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "item", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "openai.types.beta.realtime.conversation_item.ConversationItem", {".class": "LiteralType", "fallback": "builtins.str", "value": "conversation.item.retrieved"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ConversationItemRetrieved", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "event_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved.event_id", "name": "event_id", "setter_type": null, "type": "builtins.str"}}, "item": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved.item", "name": "item", "setter_type": null, "type": "openai.types.beta.realtime.conversation_item.ConversationItem"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved.type", "name": "type", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "conversation.item.retrieved"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ConversationItemTruncatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_truncated_event.ConversationItemTruncatedEvent", "kind": "Gdef", "module_public": false}, "ErrorEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.error_event.ErrorEvent", "kind": "Gdef", "module_public": false}, "InputAudioBufferClearedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_cleared_event.InputAudioBufferClearedEvent", "kind": "Gdef", "module_public": false}, "InputAudioBufferCommittedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_committed_event.InputAudioBufferCommittedEvent", "kind": "Gdef", "module_public": false}, "InputAudioBufferSpeechStartedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_speech_started_event.InputAudioBufferSpeechStartedEvent", "kind": "Gdef", "module_public": false}, "InputAudioBufferSpeechStoppedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_speech_stopped_event.InputAudioBufferSpeechStoppedEvent", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "OutputAudioBufferCleared": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared", "name": "OutputAudioBufferCleared", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 85, "name": "event_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 88, "name": "response_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 91, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.cleared"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.beta.realtime.realtime_server_event", "mro": ["openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3], "arg_names": ["self", "_request_id", "event_id", "response_id", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3], "arg_names": ["self", "_request_id", "event_id", "response_id", "type"], "arg_types": ["openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.cleared"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of OutputAudioBufferCleared", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "response_id", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "response_id", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.cleared"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of OutputAudioBufferCleared", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "response_id", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.cleared"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of OutputAudioBufferCleared", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "event_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared.event_id", "name": "event_id", "setter_type": null, "type": "builtins.str"}}, "response_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared.response_id", "name": "response_id", "setter_type": null, "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared.type", "name": "type", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.cleared"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OutputAudioBufferStarted": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted", "name": "OutputAudioBufferStarted", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 63, "name": "event_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 66, "name": "response_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 69, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.started"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.beta.realtime.realtime_server_event", "mro": ["openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3], "arg_names": ["self", "_request_id", "event_id", "response_id", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3], "arg_names": ["self", "_request_id", "event_id", "response_id", "type"], "arg_types": ["openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.started"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of OutputAudioBufferStarted", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "response_id", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "response_id", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.started"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of OutputAudioBufferStarted", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "response_id", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.started"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of OutputAudioBufferStarted", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "event_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted.event_id", "name": "event_id", "setter_type": null, "type": "builtins.str"}}, "response_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted.response_id", "name": "response_id", "setter_type": null, "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted.type", "name": "type", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.started"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "OutputAudioBufferStopped": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped", "name": "OutputAudioBufferStopped", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 74, "name": "event_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 77, "name": "response_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 80, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.stopped"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.beta.realtime.realtime_server_event", "mro": ["openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3], "arg_names": ["self", "_request_id", "event_id", "response_id", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3], "arg_names": ["self", "_request_id", "event_id", "response_id", "type"], "arg_types": ["openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.stopped"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of OutputAudioBufferStopped", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "response_id", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "response_id", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.stopped"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of OutputAudioBufferStopped", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "event_id", "response_id", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.stopped"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of OutputAudioBufferStopped", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "event_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped.event_id", "name": "event_id", "setter_type": null, "type": "builtins.str"}}, "response_id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped.response_id", "name": "response_id", "setter_type": null, "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped.type", "name": "type", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "output_audio_buffer.stopped"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PropertyInfo": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.PropertyInfo", "kind": "Gdef", "module_public": false}, "RateLimitsUpdatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.rate_limits_updated_event.RateLimitsUpdatedEvent", "kind": "Gdef", "module_public": false}, "RealtimeServerEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "openai.types.beta.realtime.realtime_server_event.RealtimeServerEvent", "line": 95, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["openai.types.beta.realtime.conversation_created_event.ConversationCreatedEvent", "openai.types.beta.realtime.conversation_item_created_event.ConversationItemCreatedEvent", "openai.types.beta.realtime.conversation_item_deleted_event.ConversationItemDeletedEvent", "openai.types.beta.realtime.conversation_item_input_audio_transcription_completed_event.ConversationItemInputAudioTranscriptionCompletedEvent", "openai.types.beta.realtime.conversation_item_input_audio_transcription_delta_event.ConversationItemInputAudioTranscriptionDeltaEvent", "openai.types.beta.realtime.conversation_item_input_audio_transcription_failed_event.ConversationItemInputAudioTranscriptionFailedEvent", "openai.types.beta.realtime.realtime_server_event.ConversationItemRetrieved", "openai.types.beta.realtime.conversation_item_truncated_event.ConversationItemTruncatedEvent", "openai.types.beta.realtime.error_event.ErrorEvent", "openai.types.beta.realtime.input_audio_buffer_cleared_event.InputAudioBufferClearedEvent", "openai.types.beta.realtime.input_audio_buffer_committed_event.InputAudioBufferCommittedEvent", "openai.types.beta.realtime.input_audio_buffer_speech_started_event.InputAudioBufferSpeechStartedEvent", "openai.types.beta.realtime.input_audio_buffer_speech_stopped_event.InputAudioBufferSpeechStoppedEvent", "openai.types.beta.realtime.rate_limits_updated_event.RateLimitsUpdatedEvent", "openai.types.beta.realtime.response_audio_delta_event.ResponseAudioDeltaEvent", "openai.types.beta.realtime.response_audio_done_event.ResponseAudioDoneEvent", "openai.types.beta.realtime.response_audio_transcript_delta_event.ResponseAudioTranscriptDeltaEvent", "openai.types.beta.realtime.response_audio_transcript_done_event.ResponseAudioTranscriptDoneEvent", "openai.types.beta.realtime.response_content_part_added_event.ResponseContentPartAddedEvent", "openai.types.beta.realtime.response_content_part_done_event.ResponseContentPartDoneEvent", "openai.types.beta.realtime.response_created_event.ResponseCreatedEvent", "openai.types.beta.realtime.response_done_event.ResponseDoneEvent", "openai.types.beta.realtime.response_function_call_arguments_delta_event.ResponseFunctionCallArgumentsDeltaEvent", "openai.types.beta.realtime.response_function_call_arguments_done_event.ResponseFunctionCallArgumentsDoneEvent", "openai.types.beta.realtime.response_output_item_added_event.ResponseOutputItemAddedEvent", "openai.types.beta.realtime.response_output_item_done_event.ResponseOutputItemDoneEvent", "openai.types.beta.realtime.response_text_delta_event.ResponseTextDeltaEvent", "openai.types.beta.realtime.response_text_done_event.ResponseTextDoneEvent", "openai.types.beta.realtime.session_created_event.SessionCreatedEvent", "openai.types.beta.realtime.session_updated_event.SessionUpdatedEvent", "openai.types.beta.realtime.transcription_session_updated_event.TranscriptionSessionUpdatedEvent", "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStarted", "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferStopped", "openai.types.beta.realtime.realtime_server_event.OutputAudioBufferCleared"], "uses_pep604_syntax": false}}}, "ResponseAudioDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_audio_delta_event.ResponseAudioDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseAudioDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_audio_done_event.ResponseAudioDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseAudioTranscriptDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_audio_transcript_delta_event.ResponseAudioTranscriptDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseAudioTranscriptDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_audio_transcript_done_event.ResponseAudioTranscriptDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseContentPartAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_content_part_added_event.ResponseContentPartAddedEvent", "kind": "Gdef", "module_public": false}, "ResponseContentPartDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_content_part_done_event.ResponseContentPartDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_created_event.ResponseCreatedEvent", "kind": "Gdef", "module_public": false}, "ResponseDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_done_event.ResponseDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseFunctionCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_function_call_arguments_delta_event.ResponseFunctionCallArgumentsDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseFunctionCallArgumentsDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_function_call_arguments_done_event.ResponseFunctionCallArgumentsDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseOutputItemAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_output_item_added_event.ResponseOutputItemAddedEvent", "kind": "Gdef", "module_public": false}, "ResponseOutputItemDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_output_item_done_event.ResponseOutputItemDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseTextDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_text_delta_event.ResponseTextDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseTextDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_text_done_event.ResponseTextDoneEvent", "kind": "Gdef", "module_public": false}, "SessionCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session_created_event.SessionCreatedEvent", "kind": "Gdef", "module_public": false}, "SessionUpdatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session_updated_event.SessionUpdatedEvent", "kind": "Gdef", "module_public": false}, "TranscriptionSessionUpdatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.transcription_session_updated_event.TranscriptionSessionUpdatedEvent", "kind": "Gdef", "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.types.beta.realtime.realtime_server_event.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.realtime_server_event.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_server_event.py"}