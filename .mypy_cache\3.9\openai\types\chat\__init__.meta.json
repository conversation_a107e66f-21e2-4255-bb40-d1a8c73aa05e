{"data_mtime": 1754400649, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 16, 17, 18, 19, 20, 21, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 42, 45, 48, 51, 54, 57, 60, 63, 66, 69, 72, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.chat.chat_completion", "openai.types.chat.chat_completion_role", "openai.types.chat.chat_completion_tool", "openai.types.chat.chat_completion_audio", "openai.types.chat.chat_completion_chunk", "openai.types.chat.completion_list_params", "openai.types.chat.parsed_chat_completion", "openai.types.chat.chat_completion_deleted", "openai.types.chat.chat_completion_message", "openai.types.chat.chat_completion_modality", "openai.types.chat.completion_create_params", "openai.types.chat.completion_update_params", "openai.types.chat.parsed_function_tool_call", "openai.types.chat.chat_completion_tool_param", "openai.types.chat.chat_completion_audio_param", "openai.types.chat.chat_completion_message_param", "openai.types.chat.chat_completion_store_message", "openai.types.chat.chat_completion_token_logprob", "openai.types.chat.chat_completion_reasoning_effort", "openai.types.chat.chat_completion_content_part_text", "openai.types.chat.chat_completion_message_tool_call", "openai.types.chat.chat_completion_content_part_image", "openai.types.chat.chat_completion_content_part_param", "openai.types.chat.chat_completion_tool_message_param", "openai.types.chat.chat_completion_user_message_param", "openai.types.chat.chat_completion_stream_options_param", "openai.types.chat.chat_completion_system_message_param", "openai.types.chat.chat_completion_function_message_param", "openai.types.chat.chat_completion_assistant_message_param", "openai.types.chat.chat_completion_content_part_text_param", "openai.types.chat.chat_completion_developer_message_param", "openai.types.chat.chat_completion_message_tool_call_param", "openai.types.chat.chat_completion_named_tool_choice_param", "openai.types.chat.chat_completion_content_part_image_param", "openai.types.chat.chat_completion_prediction_content_param", "openai.types.chat.chat_completion_tool_choice_option_param", "openai.types.chat.chat_completion_content_part_refusal_param", "openai.types.chat.chat_completion_function_call_option_param", "openai.types.chat.chat_completion_content_part_input_audio_param", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "7df139ed766c75875d21e4371b6cad8adf58749d", "id": "openai.types.chat", "ignore_all": true, "interface_hash": "51072df95246e8f1c1baaefeb40a99b35a2860ab", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\__init__.py", "plugin_data": null, "size": 4499, "suppressed": [], "version_id": "1.17.1"}