{"data_mtime": 1754400651, "dep_lines": [16, 25, 25, 27, 28, 29, 30, 31, 32, 25, 10, 11, 12, 13, 14, 15, 24, 26, 3, 5, 6, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 5, 5, 20, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.evals.runs.output_items", "openai.types.evals.run_list_params", "openai.types.evals.run_create_params", "openai.types.shared_params.metadata", "openai.types.evals.run_list_response", "openai.types.evals.run_cancel_response", "openai.types.evals.run_create_response", "openai.types.evals.run_delete_response", "openai.types.evals.run_retrieve_response", "openai.types.evals", "openai._legacy_response", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai.pagination", "openai._base_client", "__future__", "typing", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai.types", "openai.types.chat", "openai.types.chat.chat_completion_tool_param", "openai.types.evals.create_eval_completions_run_data_source_param", "openai.types.evals.create_eval_jsonl_run_data_source_param", "openai.types.responses", "openai.types.responses.computer_tool_param", "openai.types.responses.easy_input_message_param", "openai.types.responses.file_search_tool_param", "openai.types.responses.function_tool_param", "openai.types.responses.response_format_text_json_schema_config_param", "openai.types.responses.response_input_file_param", "openai.types.responses.response_input_image_param", "openai.types.responses.response_input_text_param", "openai.types.responses.tool_param", "openai.types.responses.web_search_tool_param", "openai.types.shared_params", "openai.types.shared_params.comparison_filter", "openai.types.shared_params.compound_filter", "openai.types.shared_params.function_definition", "openai.types.shared_params.response_format_json_object", "openai.types.shared_params.response_format_json_schema", "openai.types.shared_params.response_format_text", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "8e5394946a6d36cde8cabbb0c4548bd318cb8a30", "id": "openai.resources.evals.runs.runs", "ignore_all": true, "interface_hash": "10a3c48ad2c8871250346dad8ac36c809d41fe0b", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\evals\\runs\\runs.py", "plugin_data": null, "size": 24455, "suppressed": [], "version_id": "1.17.1"}