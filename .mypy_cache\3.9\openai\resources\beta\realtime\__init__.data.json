{".class": "MypyFile", "_fullname": "openai.resources.beta.realtime", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncRealtime": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.realtime.AsyncRealtime", "kind": "Gdef"}, "AsyncRealtimeWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithRawResponse", "kind": "Gdef"}, "AsyncRealtimeWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.realtime.AsyncRealtimeWithStreamingResponse", "kind": "Gdef"}, "AsyncSessions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.AsyncSessions", "kind": "Gdef"}, "AsyncSessionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.AsyncSessionsWithRawResponse", "kind": "Gdef"}, "AsyncSessionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.AsyncSessionsWithStreamingResponse", "kind": "Gdef"}, "AsyncTranscriptionSessions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessions", "kind": "Gdef"}, "AsyncTranscriptionSessionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessionsWithRawResponse", "kind": "Gdef"}, "AsyncTranscriptionSessionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.AsyncTranscriptionSessionsWithStreamingResponse", "kind": "Gdef"}, "Realtime": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.realtime.Realtime", "kind": "Gdef"}, "RealtimeWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.realtime.RealtimeWithRawResponse", "kind": "Gdef"}, "RealtimeWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.realtime.RealtimeWithStreamingResponse", "kind": "Gdef"}, "Sessions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.Sessions", "kind": "Gdef"}, "SessionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.SessionsWithRawResponse", "kind": "Gdef"}, "SessionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.sessions.SessionsWithStreamingResponse", "kind": "Gdef"}, "TranscriptionSessions": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessions", "kind": "Gdef"}, "TranscriptionSessionsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessionsWithRawResponse", "kind": "Gdef"}, "TranscriptionSessionsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.realtime.transcription_sessions.TranscriptionSessionsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.beta.realtime.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.realtime.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\realtime\\__init__.py"}