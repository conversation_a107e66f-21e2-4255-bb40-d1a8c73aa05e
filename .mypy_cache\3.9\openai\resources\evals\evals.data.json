{".class": "MypyFile", "_fullname": "openai.resources.evals.evals", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.AsyncAPIResource", "kind": "Gdef", "module_public": false}, "AsyncCursorPage": {".class": "SymbolTableNode", "cross_ref": "openai.pagination.AsyncCursorPage", "kind": "Gdef", "module_public": false}, "AsyncEvals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.AsyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.evals.evals.AsyncEvals", "name": "AsyncEvals", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.evals.evals.AsyncEvals", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.evals.evals", "mro": ["openai.resources.evals.evals.AsyncEvals", "openai._resource.AsyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "data_source_config", "testing_criteria", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvals.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "data_source_config", "testing_criteria", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.evals.evals.AsyncEvals", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.DataSourceConfig"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.TestingCriterion"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncEvals", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.eval_create_response.EvalCreateResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvals.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.evals.evals.AsyncEvals", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of AsyncEvals", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.eval_delete_response.EvalDeleteResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "after", "limit", "order", "order_by", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvals.list", "name": "list", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "after", "limit", "order", "order_by", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.evals.evals.AsyncEvals", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "created_at"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "updated_at"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list of AsyncEvals", "ret_type": {".class": "Instance", "args": ["openai.types.eval_list_response.EvalListResponse", {".class": "Instance", "args": ["openai.types.eval_list_response.EvalListResponse"], "extra_attrs": null, "type_ref": "openai.pagination.AsyncCursorPage"}], "extra_attrs": null, "type_ref": "openai._base_client.AsyncPaginator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvals.retrieve", "name": "retrieve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.evals.evals.AsyncEvals", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retrieve of AsyncEvals", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.eval_retrieve_response.EvalRetrieveResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvals.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.AsyncEvals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncEvals", "ret_type": "openai.resources.evals.runs.runs.AsyncRuns", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvals.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.AsyncEvals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncEvals", "ret_type": "openai.resources.evals.runs.runs.AsyncRuns", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvals.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.evals.evals.AsyncEvals", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of AsyncEvals", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.eval_update_response.EvalUpdateResponse"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvals.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.AsyncEvals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncEvals", "ret_type": "openai.resources.evals.evals.AsyncEvalsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvals.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.AsyncEvals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncEvals", "ret_type": "openai.resources.evals.evals.AsyncEvalsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvals.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.AsyncEvals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncEvals", "ret_type": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvals.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.AsyncEvals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncEvals", "ret_type": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.evals.evals.AsyncEvals.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.evals.evals.AsyncEvals", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncEvalsWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse", "name": "AsyncEvalsWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.evals.evals", "mro": ["openai.resources.evals.evals.AsyncEvalsWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "evals"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "evals"], "arg_types": ["openai.resources.evals.evals.AsyncEvalsWithRawResponse", "openai.resources.evals.evals.AsyncEvals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncEvalsWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse._evals", "name": "_evals", "setter_type": null, "type": "openai.resources.evals.evals.AsyncEvals"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["data_source_config", "testing_criteria", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.DataSourceConfig"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.TestingCriterion"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_create_response.EvalCreateResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_delete_response.EvalDeleteResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse.list", "name": "list", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["after", "limit", "order", "order_by", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "created_at"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "updated_at"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_list_response.EvalListResponse"], "extra_attrs": null, "type_ref": "openai.pagination.AsyncCursorPage"}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_retrieve_response.EvalRetrieveResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.AsyncEvalsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncEvalsWithRawResponse", "ret_type": "openai.resources.evals.runs.runs.AsyncRunsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.AsyncEvalsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncEvalsWithRawResponse", "ret_type": "openai.resources.evals.runs.runs.AsyncRunsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["eval_id", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_update_response.EvalUpdateResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.evals.evals.AsyncEvalsWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.evals.evals.AsyncEvalsWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncEvalsWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse", "name": "AsyncEvalsWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.evals.evals", "mro": ["openai.resources.evals.evals.AsyncEvalsWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "evals"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "evals"], "arg_types": ["openai.resources.evals.evals.AsyncEvalsWithStreamingResponse", "openai.resources.evals.evals.AsyncEvals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncEvalsWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse._evals", "name": "_evals", "setter_type": null, "type": "openai.resources.evals.evals.AsyncEvals"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["data_source_config", "testing_criteria", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.DataSourceConfig"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.TestingCriterion"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_create_response.EvalCreateResponse"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_delete_response.EvalDeleteResponse"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse.list", "name": "list", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["after", "limit", "order", "order_by", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "created_at"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "updated_at"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_list_response.EvalListResponse"], "extra_attrs": null, "type_ref": "openai.pagination.AsyncCursorPage"}], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_retrieve_response.EvalRetrieveResponse"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.AsyncEvalsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncEvalsWithStreamingResponse", "ret_type": "openai.resources.evals.runs.runs.AsyncRunsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.AsyncEvalsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of AsyncEvalsWithStreamingResponse", "ret_type": "openai.resources.evals.runs.runs.AsyncRunsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["eval_id", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_update_response.EvalUpdateResponse"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.evals.evals.AsyncEvalsWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncPaginator": {".class": "SymbolTableNode", "cross_ref": "openai._base_client.AsyncPaginator", "kind": "Gdef", "module_public": false}, "AsyncRuns": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.AsyncRuns", "kind": "Gdef", "module_public": false}, "AsyncRunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.AsyncRunsWithRawResponse", "kind": "Gdef", "module_public": false}, "AsyncRunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.AsyncRunsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "Body": {".class": "SymbolTableNode", "cross_ref": "openai._types.Body", "kind": "Gdef", "module_public": false}, "EvalCreateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_create_response.EvalCreateResponse", "kind": "Gdef", "module_public": false}, "EvalDeleteResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_delete_response.EvalDeleteResponse", "kind": "Gdef", "module_public": false}, "EvalListResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_list_response.EvalListResponse", "kind": "Gdef", "module_public": false}, "EvalRetrieveResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_retrieve_response.EvalRetrieveResponse", "kind": "Gdef", "module_public": false}, "EvalUpdateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_update_response.EvalUpdateResponse", "kind": "Gdef", "module_public": false}, "Evals": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.SyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.evals.evals.Evals", "name": "Evals", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.evals.evals.Evals", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.evals.evals", "mro": ["openai.resources.evals.evals.Evals", "openai._resource.SyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "data_source_config", "testing_criteria", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.evals.evals.Evals.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "data_source_config", "testing_criteria", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.evals.evals.Evals", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.DataSourceConfig"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.TestingCriterion"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Evals", "ret_type": "openai.types.eval_create_response.EvalCreateResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.evals.evals.Evals.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.evals.evals.Evals", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of Evals", "ret_type": "openai.types.eval_delete_response.EvalDeleteResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "after", "limit", "order", "order_by", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.evals.evals.Evals.list", "name": "list", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "after", "limit", "order", "order_by", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.evals.evals.Evals", {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "created_at"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "updated_at"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list of Evals", "ret_type": {".class": "Instance", "args": ["openai.types.eval_list_response.EvalListResponse"], "extra_attrs": null, "type_ref": "openai.pagination.SyncCursorPage"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.evals.evals.Evals.retrieve", "name": "retrieve", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.evals.evals.Evals", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "retrieve of <PERSON>ls", "ret_type": "openai.types.eval_retrieve_response.EvalRetrieveResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.evals.evals.Evals.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.Evals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of Evals", "ret_type": "openai.resources.evals.runs.runs.Runs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.evals.evals.Evals.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.Evals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of Evals", "ret_type": "openai.resources.evals.runs.runs.Runs", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.evals.evals.Evals.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "eval_id", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.evals.evals.Evals", "builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of Evals", "ret_type": "openai.types.eval_update_response.EvalUpdateResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.evals.evals.Evals.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.Evals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Evals", "ret_type": "openai.resources.evals.evals.EvalsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.evals.evals.Evals.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.Evals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Evals", "ret_type": "openai.resources.evals.evals.EvalsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.evals.evals.Evals.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.Evals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Evals", "ret_type": "openai.resources.evals.evals.EvalsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.evals.evals.Evals.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.Evals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Evals", "ret_type": "openai.resources.evals.evals.EvalsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.evals.evals.Evals.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.evals.evals.Evals", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EvalsWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.evals.evals.EvalsWithRawResponse", "name": "EvalsWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.evals.evals.EvalsWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.evals.evals", "mro": ["openai.resources.evals.evals.EvalsWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "evals"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.evals.evals.EvalsWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "evals"], "arg_types": ["openai.resources.evals.evals.EvalsWithRawResponse", "openai.resources.evals.evals.Evals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of EvalsWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithRawResponse._evals", "name": "_evals", "setter_type": null, "type": "openai.resources.evals.evals.Evals"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithRawResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["data_source_config", "testing_criteria", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.DataSourceConfig"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.TestingCriterion"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.eval_create_response.EvalCreateResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithRawResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.eval_delete_response.EvalDeleteResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithRawResponse.list", "name": "list", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["after", "limit", "order", "order_by", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "created_at"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "updated_at"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_list_response.EvalListResponse"], "extra_attrs": null, "type_ref": "openai.pagination.SyncCursorPage"}], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithRawResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.eval_retrieve_response.EvalRetrieveResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.evals.evals.EvalsWithRawResponse.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.EvalsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of EvalsWithRawResponse", "ret_type": "openai.resources.evals.runs.runs.RunsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithRawResponse.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.EvalsWithRawResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of EvalsWithRawResponse", "ret_type": "openai.resources.evals.runs.runs.RunsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithRawResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["eval_id", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.eval_update_response.EvalUpdateResponse"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.evals.evals.EvalsWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.evals.evals.EvalsWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "EvalsWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse", "name": "EvalsWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.evals.evals", "mro": ["openai.resources.evals.evals.EvalsWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "evals"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "evals"], "arg_types": ["openai.resources.evals.evals.EvalsWithStreamingResponse", "openai.resources.evals.evals.Evals"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of EvalsWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_evals": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse._evals", "name": "_evals", "setter_type": null, "type": "openai.resources.evals.evals.Evals"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["data_source_config", "testing_criteria", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.DataSourceConfig"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.eval_create_params.TestingCriterion"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_create_response.EvalCreateResponse"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse.delete", "name": "delete", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_delete_response.EvalDeleteResponse"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse.list", "name": "list", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["after", "limit", "order", "order_by", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "asc"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "desc"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "created_at"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "updated_at"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_list_response.EvalListResponse"], "extra_attrs": null, "type_ref": "openai.pagination.SyncCursorPage"}], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "retrieve": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse.retrieve", "name": "retrieve", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5], "arg_names": ["eval_id", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_retrieve_response.EvalRetrieveResponse"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "runs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse.runs", "name": "runs", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.EvalsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of EvalsWithStreamingResponse", "ret_type": "openai.resources.evals.runs.runs.RunsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse.runs", "name": "runs", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.evals.evals.EvalsWithStreamingResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "runs of EvalsWithStreamingResponse", "ret_type": "openai.resources.evals.runs.runs.RunsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "update": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse.update", "name": "update", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5], "arg_names": ["eval_id", "metadata", "name", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.metadata.Metadata"}, {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.eval_update_response.EvalUpdateResponse"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.evals.evals.EvalsWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.evals.evals.EvalsWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Headers": {".class": "SymbolTableNode", "cross_ref": "openai._types.Headers", "kind": "Gdef", "module_public": false}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared_params.metadata.Metadata", "kind": "Gdef", "module_public": false}, "NOT_GIVEN": {".class": "SymbolTableNode", "cross_ref": "openai._types.NOT_GIVEN", "kind": "Gdef", "module_public": false}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Query": {".class": "SymbolTableNode", "cross_ref": "openai._types.Query", "kind": "Gdef", "module_public": false}, "Runs": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.Runs", "kind": "Gdef", "module_public": false}, "RunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.RunsWithRawResponse", "kind": "Gdef", "module_public": false}, "RunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.evals.runs.runs.RunsWithStreamingResponse", "kind": "Gdef", "module_public": false}, "SyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.SyncAPIResource", "kind": "Gdef", "module_public": false}, "SyncCursorPage": {".class": "SymbolTableNode", "cross_ref": "openai.pagination.SyncCursorPage", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.evals.evals.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.evals.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.evals.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.evals.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.evals.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.evals.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.evals.evals.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_legacy_response": {".class": "SymbolTableNode", "cross_ref": "openai._legacy_response", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "async_maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.async_maybe_transform", "kind": "Gdef", "module_public": false}, "async_to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.async_to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "openai._compat.cached_property", "kind": "Gdef", "module_public": false}, "eval_create_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_create_params", "kind": "Gdef", "module_public": false}, "eval_list_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_list_params", "kind": "Gdef", "module_public": false}, "eval_update_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.eval_update_params", "kind": "Gdef", "module_public": false}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef", "module_public": false}, "make_request_options": {".class": "SymbolTableNode", "cross_ref": "openai._base_client.make_request_options", "kind": "Gdef", "module_public": false}, "maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.maybe_transform", "kind": "Gdef", "module_public": false}, "to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\evals\\evals.py"}