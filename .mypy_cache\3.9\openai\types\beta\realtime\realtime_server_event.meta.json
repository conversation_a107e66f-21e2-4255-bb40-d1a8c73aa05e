{"data_mtime": 1754400649, "dep_lines": [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 6, 7, 3, 4, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.beta.realtime.error_event", "openai.types.beta.realtime.conversation_item", "openai.types.beta.realtime.response_done_event", "openai.types.beta.realtime.session_created_event", "openai.types.beta.realtime.session_updated_event", "openai.types.beta.realtime.response_created_event", "openai.types.beta.realtime.response_text_done_event", "openai.types.beta.realtime.rate_limits_updated_event", "openai.types.beta.realtime.response_audio_done_event", "openai.types.beta.realtime.response_text_delta_event", "openai.types.beta.realtime.conversation_created_event", "openai.types.beta.realtime.response_audio_delta_event", "openai.types.beta.realtime.conversation_item_created_event", "openai.types.beta.realtime.conversation_item_deleted_event", "openai.types.beta.realtime.response_output_item_done_event", "openai.types.beta.realtime.input_audio_buffer_cleared_event", "openai.types.beta.realtime.response_content_part_done_event", "openai.types.beta.realtime.response_output_item_added_event", "openai.types.beta.realtime.conversation_item_truncated_event", "openai.types.beta.realtime.response_content_part_added_event", "openai.types.beta.realtime.input_audio_buffer_committed_event", "openai.types.beta.realtime.transcription_session_updated_event", "openai.types.beta.realtime.response_audio_transcript_done_event", "openai.types.beta.realtime.response_audio_transcript_delta_event", "openai.types.beta.realtime.input_audio_buffer_speech_started_event", "openai.types.beta.realtime.input_audio_buffer_speech_stopped_event", "openai.types.beta.realtime.response_function_call_arguments_done_event", "openai.types.beta.realtime.response_function_call_arguments_delta_event", "openai.types.beta.realtime.conversation_item_input_audio_transcription_delta_event", "openai.types.beta.realtime.conversation_item_input_audio_transcription_failed_event", "openai.types.beta.realtime.conversation_item_input_audio_transcription_completed_event", "openai._utils", "openai._models", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "321128d0f78390148c37131cc80461419ef9daf5", "id": "openai.types.beta.realtime.realtime_server_event", "ignore_all": true, "interface_hash": "1b54323669babad032235606c164601a21e0c886", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_server_event.py", "plugin_data": null, "size": 5408, "suppressed": [], "version_id": "1.17.1"}