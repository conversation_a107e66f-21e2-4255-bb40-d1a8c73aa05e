{"data_mtime": 1754400654, "dep_lines": [21, 24, 27, 30, 33, 36, 39, 42, 45, 48, 51, 54, 57, 60, 63, 66, 69, 72, 75, 83, 84, 88, 109, 118, 119, 127, 156, 157, 169, 170, 180, 184, 194, 224, 231, 244, 16, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["google.ai.generativelanguage_v1beta.services.cache_service.async_client", "google.ai.generativelanguage_v1beta.services.cache_service.client", "google.ai.generativelanguage_v1beta.services.discuss_service.async_client", "google.ai.generativelanguage_v1beta.services.discuss_service.client", "google.ai.generativelanguage_v1beta.services.file_service.async_client", "google.ai.generativelanguage_v1beta.services.file_service.client", "google.ai.generativelanguage_v1beta.services.generative_service.async_client", "google.ai.generativelanguage_v1beta.services.generative_service.client", "google.ai.generativelanguage_v1beta.services.model_service.async_client", "google.ai.generativelanguage_v1beta.services.model_service.client", "google.ai.generativelanguage_v1beta.services.permission_service.async_client", "google.ai.generativelanguage_v1beta.services.permission_service.client", "google.ai.generativelanguage_v1beta.services.prediction_service.async_client", "google.ai.generativelanguage_v1beta.services.prediction_service.client", "google.ai.generativelanguage_v1beta.services.retriever_service.async_client", "google.ai.generativelanguage_v1beta.services.retriever_service.client", "google.ai.generativelanguage_v1beta.services.text_service.async_client", "google.ai.generativelanguage_v1beta.services.text_service.client", "google.ai.generativelanguage_v1beta.types.cache_service", "google.ai.generativelanguage_v1beta.types.cached_content", "google.ai.generativelanguage_v1beta.types.citation", "google.ai.generativelanguage_v1beta.types.content", "google.ai.generativelanguage_v1beta.types.discuss_service", "google.ai.generativelanguage_v1beta.types.file", "google.ai.generativelanguage_v1beta.types.file_service", "google.ai.generativelanguage_v1beta.types.generative_service", "google.ai.generativelanguage_v1beta.types.model", "google.ai.generativelanguage_v1beta.types.model_service", "google.ai.generativelanguage_v1beta.types.permission", "google.ai.generativelanguage_v1beta.types.permission_service", "google.ai.generativelanguage_v1beta.types.prediction_service", "google.ai.generativelanguage_v1beta.types.retriever", "google.ai.generativelanguage_v1beta.types.retriever_service", "google.ai.generativelanguage_v1beta.types.safety", "google.ai.generativelanguage_v1beta.types.text_service", "google.ai.generativelanguage_v1beta.types.tuned_model", "google.ai.generativelanguage.gapic_version", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "a6640ee9cbe2e4fbac36a4ed7397a13c910cbaf1", "id": "google.ai.generativelanguage", "ignore_all": true, "interface_hash": "2a5ffa293ce98d3beb7c141dcd6d9247b6167bec", "mtime": 1754241452, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage\\__init__.py", "plugin_data": null, "size": 11838, "suppressed": [], "version_id": "1.17.1"}