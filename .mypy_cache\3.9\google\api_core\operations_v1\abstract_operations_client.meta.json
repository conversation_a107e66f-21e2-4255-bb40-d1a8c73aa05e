{"data_mtime": 1754400651, "dep_lines": [22, 21, 26, 18, 19, 20, 21, 29, 31, 18, 29, 31, 16, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 30, 32], "dep_prios": [5, 10, 5, 10, 10, 10, 20, 10, 10, 20, 20, 20, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10], "dependencies": ["google.api_core.operations_v1.transports.base", "google.api_core.operations_v1.pagers", "google.api_core.operations_v1.abstract_operations_base_client", "google.api_core.client_options", "google.api_core.gapic_v1", "google.api_core.retry", "google.api_core.operations_v1", "google.auth.credentials", "google.oauth2.service_account", "google.api_core", "google.auth", "google.oauth2", "typing", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.gapic_v1.routing_header", "google.api_core.operations_v1.pagers_base", "google.api_core.operations_v1.transports", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base"], "hash": "2f398f0f67f135ed337cf186bc32693bd1e75f71", "id": "google.api_core.operations_v1.abstract_operations_client", "ignore_all": true, "interface_hash": "9ae0ee0af7d4800e293d7ddaeb6ac15e924ba780", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\abstract_operations_client.py", "plugin_data": null, "size": 16073, "suppressed": ["google.longrunning", "grpc"], "version_id": "1.17.1"}