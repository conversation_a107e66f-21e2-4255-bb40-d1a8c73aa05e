{".class": "MypyFile", "_fullname": "openai.resources.fine_tuning.jobs", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncCheckpoints": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.checkpoints.AsyncCheckpoints", "kind": "Gdef"}, "AsyncCheckpointsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.checkpoints.AsyncCheckpointsWithRawResponse", "kind": "Gdef"}, "AsyncCheckpointsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.checkpoints.AsyncCheckpointsWithStreamingResponse", "kind": "Gdef"}, "AsyncJobs": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.AsyncJobs", "kind": "Gdef"}, "AsyncJobsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.AsyncJobsWithRawResponse", "kind": "Gdef"}, "AsyncJobsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.AsyncJobsWithStreamingResponse", "kind": "Gdef"}, "Checkpoints": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.checkpoints.Checkpoints", "kind": "Gdef"}, "CheckpointsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.checkpoints.CheckpointsWithRawResponse", "kind": "Gdef"}, "CheckpointsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.checkpoints.CheckpointsWithStreamingResponse", "kind": "Gdef"}, "Jobs": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.Jobs", "kind": "Gdef"}, "JobsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.JobsWithRawResponse", "kind": "Gdef"}, "JobsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.jobs.jobs.JobsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.fine_tuning.jobs.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.jobs.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.jobs.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.jobs.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.jobs.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.jobs.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.jobs.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.jobs.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\jobs\\__init__.py"}