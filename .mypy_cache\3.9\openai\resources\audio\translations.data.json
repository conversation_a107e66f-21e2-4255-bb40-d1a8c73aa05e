{".class": "MypyFile", "_fullname": "openai.resources.audio.translations", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.AsyncAPIResource", "kind": "Gdef", "module_public": false}, "AsyncTranslations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.AsyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.translations.AsyncTranslations", "name": "AsyncTranslations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.translations.AsyncTranslations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.translations", "mro": ["openai.resources.audio.translations.AsyncTranslations", "openai._resource.AsyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "openai.resources.audio.translations.AsyncTranslations.create", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "prompt", "response_format", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_trivial_self"], "fullname": "openai.resources.audio.translations.AsyncTranslations.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "prompt", "response_format", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_response_format.AudioResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncTranslations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "UnionType", "items": ["openai.types.audio.translation.Translation", "openai.types.audio.translation_verbose.TranslationVerbose", "builtins.str"], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.translations.AsyncTranslations.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncTranslations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.audio.translation.Translation"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.audio.translations.AsyncTranslations.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncTranslations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.audio.translation.Translation"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.translations.AsyncTranslations.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncTranslations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.audio.translation_verbose.TranslationVerbose"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.audio.translations.AsyncTranslations.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncTranslations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.audio.translation_verbose.TranslationVerbose"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.translations.AsyncTranslations.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncTranslations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.audio.translations.AsyncTranslations.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncTranslations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncTranslations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.audio.translation.Translation"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncTranslations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "openai.types.audio.translation_verbose.TranslationVerbose"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of AsyncTranslations", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.translations.AsyncTranslations.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncTranslations", "ret_type": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.translations.AsyncTranslations.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of AsyncTranslations", "ret_type": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.translations.AsyncTranslations.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncTranslations", "ret_type": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.translations.AsyncTranslations.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.translations.AsyncTranslations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of AsyncTranslations", "ret_type": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.translations.AsyncTranslations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.translations.AsyncTranslations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncTranslationsWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse", "name": "AsyncTranslationsWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.translations", "mro": ["openai.resources.audio.translations.AsyncTranslationsWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "translations"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "translations"], "arg_types": ["openai.resources.audio.translations.AsyncTranslationsWithRawResponse", "openai.resources.audio.translations.AsyncTranslations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncTranslationsWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_translations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse._translations", "name": "_translations", "setter_type": null, "type": "openai.resources.audio.translations.AsyncTranslations"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse.create", "name": "create", "setter_type": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.audio.translation.Translation"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.audio.translation_verbose.TranslationVerbose"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.translations.AsyncTranslationsWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AsyncTranslationsWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse", "name": "AsyncTranslationsWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.translations", "mro": ["openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "translations"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "translations"], "arg_types": ["openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse", "openai.resources.audio.translations.AsyncTranslations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncTranslationsWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_translations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse._translations", "name": "_translations", "setter_type": null, "type": "openai.resources.audio.translations.AsyncTranslations"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse.create", "name": "create", "setter_type": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.audio.translation.Translation"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.audio.translation_verbose.TranslationVerbose"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "openai._response.AsyncAPIResponse"}], "extra_attrs": null, "type_ref": "openai._response.AsyncResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.translations.AsyncTranslationsWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "AudioModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio_model.AudioModel", "kind": "Gdef", "module_public": false}, "AudioResponseFormat": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio_response_format.AudioResponseFormat", "kind": "Gdef", "module_public": false}, "Body": {".class": "SymbolTableNode", "cross_ref": "openai._types.Body", "kind": "Gdef", "module_public": false}, "FileTypes": {".class": "SymbolTableNode", "cross_ref": "openai._types.FileTypes", "kind": "Gdef", "module_public": false}, "Headers": {".class": "SymbolTableNode", "cross_ref": "openai._types.Headers", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "NOT_GIVEN": {".class": "SymbolTableNode", "cross_ref": "openai._types.NOT_GIVEN", "kind": "Gdef", "module_public": false}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef", "module_public": false}, "Query": {".class": "SymbolTableNode", "cross_ref": "openai._types.Query", "kind": "Gdef", "module_public": false}, "SyncAPIResource": {".class": "SymbolTableNode", "cross_ref": "openai._resource.SyncAPIResource", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "Translation": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.translation.Translation", "kind": "Gdef", "module_public": false}, "TranslationVerbose": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.translation_verbose.TranslationVerbose", "kind": "Gdef", "module_public": false}, "Translations": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._resource.SyncAPIResource"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.translations.Translations", "name": "Translations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.translations.Translations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.translations", "mro": ["openai.resources.audio.translations.Translations", "openai._resource.SyncAPIResource", "builtins.object"], "names": {".class": "SymbolTable", "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "openai.resources.audio.translations.Translations.create", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "prompt", "response_format", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_trivial_self"], "fullname": "openai.resources.audio.translations.Translations.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "prompt", "response_format", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.Translations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Translations", "ret_type": {".class": "UnionType", "items": ["openai.types.audio.translation.Translation", "openai.types.audio.translation_verbose.TranslationVerbose", "builtins.str"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.translations.Translations.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.Translations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Translations", "ret_type": "openai.types.audio.translation.Translation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.audio.translations.Translations.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.Translations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Translations", "ret_type": "openai.types.audio.translation.Translation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.translations.Translations.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.Translations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Translations", "ret_type": "openai.types.audio.translation_verbose.TranslationVerbose", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.audio.translations.Translations.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.Translations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Translations", "ret_type": "openai.types.audio.translation_verbose.TranslationVerbose", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.translations.Translations.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.Translations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Translations", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "openai.resources.audio.translations.Translations.create", "name": "create", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.Translations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Translations", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "setter_index": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.Translations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Translations", "ret_type": "openai.types.audio.translation.Translation", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.Translations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Translations", "ret_type": "openai.types.audio.translation_verbose.TranslationVerbose", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": ["openai.resources.audio.translations.Translations", {".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Translations", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "with_raw_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.translations.Translations.with_raw_response", "name": "with_raw_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.translations.Translations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Translations", "ret_type": "openai.resources.audio.translations.TranslationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.translations.Translations.with_raw_response", "name": "with_raw_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.translations.Translations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_raw_response of Translations", "ret_type": "openai.resources.audio.translations.TranslationsWithRawResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "with_streaming_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.resources.audio.translations.Translations.with_streaming_response", "name": "with_streaming_response", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.translations.Translations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Translations", "ret_type": "openai.resources.audio.translations.TranslationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.resources.audio.translations.Translations.with_streaming_response", "name": "with_streaming_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.resources.audio.translations.Translations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "with_streaming_response of Translations", "ret_type": "openai.resources.audio.translations.TranslationsWithStreamingResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.translations.Translations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.translations.Translations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TranslationsWithRawResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.translations.TranslationsWithRawResponse", "name": "TranslationsWithRawResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.translations.TranslationsWithRawResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.translations", "mro": ["openai.resources.audio.translations.TranslationsWithRawResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "translations"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.audio.translations.TranslationsWithRawResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "translations"], "arg_types": ["openai.resources.audio.translations.TranslationsWithRawResponse", "openai.resources.audio.translations.Translations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TranslationsWithRawResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_translations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.translations.TranslationsWithRawResponse._translations", "name": "_translations", "setter_type": null, "type": "openai.resources.audio.translations.Translations"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.translations.TranslationsWithRawResponse.create", "name": "create", "setter_type": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.audio.translation.Translation"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.audio.translation_verbose.TranslationVerbose"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "openai._legacy_response.LegacyAPIResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.translations.TranslationsWithRawResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.translations.TranslationsWithRawResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TranslationsWithStreamingResponse": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.resources.audio.translations.TranslationsWithStreamingResponse", "name": "TranslationsWithStreamingResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.resources.audio.translations.TranslationsWithStreamingResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.resources.audio.translations", "mro": ["openai.resources.audio.translations.TranslationsWithStreamingResponse", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "translations"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.resources.audio.translations.TranslationsWithStreamingResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "translations"], "arg_types": ["openai.resources.audio.translations.TranslationsWithStreamingResponse", "openai.resources.audio.translations.Translations"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TranslationsWithStreamingResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_translations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.translations.TranslationsWithStreamingResponse._translations", "name": "_translations", "setter_type": null, "type": "openai.resources.audio.translations.Translations"}}, "create": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.resources.audio.translations.TranslationsWithStreamingResponse.create", "name": "create", "setter_type": null, "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [3, 3, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.audio.translation.Translation"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.audio.translation_verbose.TranslationVerbose"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3, 3, 3, 5, 5, 5, 5, 5, 5], "arg_names": ["file", "model", "response_format", "prompt", "temperature", "extra_headers", "extra_query", "extra_body", "timeout"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.FileTypes"}, {".class": "UnionType", "items": ["builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.audio_model.AudioModel"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.float", "openai._types.NotGiven"], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Headers"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai._types.Query"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.object", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.float", "httpx._config.Timeout", {".class": "NoneType"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "openai._response.APIResponse"}], "extra_attrs": null, "type_ref": "openai._response.ResponseContextManager"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.resources.audio.translations.TranslationsWithStreamingResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.resources.audio.translations.TranslationsWithStreamingResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.audio.translations.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.translations.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.translations.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.translations.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.translations.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.translations.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.audio.translations.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_get_response_format_type": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["response_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.resources.audio.translations._get_response_format_type", "name": "_get_response_format_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["response_format"], "arg_types": [{".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "text"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "srt"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "verbose_json"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vtt"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response_format_type", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": "openai.types.audio.translation.Translation"}, {".class": "TypeType", "item": "openai.types.audio.translation_verbose.TranslationVerbose"}, {".class": "TypeType", "item": "builtins.str"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_legacy_response": {".class": "SymbolTableNode", "cross_ref": "openai._legacy_response", "kind": "Gdef", "module_public": false}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.assert_never", "kind": "Gdef", "module_public": false}, "async_maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.async_maybe_transform", "kind": "Gdef", "module_public": false}, "async_to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.async_to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}, "cached_property": {".class": "SymbolTableNode", "cross_ref": "openai._compat.cached_property", "kind": "Gdef", "module_public": false}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef", "module_public": false}, "deepcopy_minimal": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.deepcopy_minimal", "kind": "Gdef", "module_public": false}, "extract_files": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.extract_files", "kind": "Gdef", "module_public": false}, "httpx": {".class": "SymbolTableNode", "cross_ref": "httpx", "kind": "Gdef", "module_public": false}, "log": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "openai.resources.audio.translations.log", "name": "log", "setter_type": null, "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "make_request_options": {".class": "SymbolTableNode", "cross_ref": "openai._base_client.make_request_options", "kind": "Gdef", "module_public": false}, "maybe_transform": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.maybe_transform", "kind": "Gdef", "module_public": false}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef", "module_public": false}, "to_streamed_response_wrapper": {".class": "SymbolTableNode", "cross_ref": "openai._response.to_streamed_response_wrapper", "kind": "Gdef", "module_public": false}, "translation_create_params": {".class": "SymbolTableNode", "cross_ref": "openai.types.audio.translation_create_params", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\audio\\translations.py"}