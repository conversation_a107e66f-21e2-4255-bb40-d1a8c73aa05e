{"data_mtime": 1754400648, "dep_lines": [5, 6, 7, 8, 9, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.fine_tuning.checkpoints.permission_create_params", "openai.types.fine_tuning.checkpoints.permission_create_response", "openai.types.fine_tuning.checkpoints.permission_delete_response", "openai.types.fine_tuning.checkpoints.permission_retrieve_params", "openai.types.fine_tuning.checkpoints.permission_retrieve_response", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "b290360ec0c30647f12e41d6e81ade1f38073df5", "id": "openai.types.fine_tuning.checkpoints", "ignore_all": true, "interface_hash": "223e0d28ba205ff828eff44a1d7793e2f6ba4f48", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\__init__.py", "plugin_data": null, "size": 588, "suppressed": [], "version_id": "1.17.1"}