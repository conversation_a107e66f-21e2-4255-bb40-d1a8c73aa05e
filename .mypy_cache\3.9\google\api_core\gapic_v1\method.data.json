{".class": "MypyFile", "_fullname": "google.api_core.gapic_v1.method", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DEFAULT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.api_core.gapic_v1.method.DEFAULT", "name": "DEFAULT", "setter_type": null, "type": "google.api_core.gapic_v1.method._MethodDefault"}}, "TimeToDeadlineTimeout": {".class": "SymbolTableNode", "cross_ref": "google.api_core.timeout.TimeToDeadlineTimeout", "kind": "Gdef"}, "USE_DEFAULT_METADATA": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.api_core.gapic_v1.method.USE_DEFAULT_METADATA", "name": "USE_DEFAULT_METADATA", "setter_type": null, "type": "builtins.object"}}, "_GapicCallable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.gapic_v1.method._GapicCallable", "name": "_GapicCallable", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.api_core.gapic_v1.method._GapicCallable", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.gapic_v1.method", "mro": ["google.api_core.gapic_v1.method._GapicCallable", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 5, 5, 5, 4], "arg_names": ["self", "args", "timeout", "retry", "compression", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.gapic_v1.method._GapicCallable.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "target", "retry", "timeout", "compression", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.gapic_v1.method._GapicCallable.__init__", "name": "__init__", "type": null}}, "_compression": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.gapic_v1.method._GapicCallable._compression", "name": "_compression", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.gapic_v1.method._GapicCallable._metadata", "name": "_metadata", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_retry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.gapic_v1.method._GapicCallable._retry", "name": "_retry", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_target": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.gapic_v1.method._GapicCallable._target", "name": "_target", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.gapic_v1.method._GapicCallable._timeout", "name": "_timeout", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.gapic_v1.method._GapicCallable.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.api_core.gapic_v1.method._GapicCallable", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_MethodDefault": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.gapic_v1.method._MethodDefault", "name": "_MethodDefault", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "google.api_core.gapic_v1.method._MethodDefault", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "google.api_core.gapic_v1.method", "mro": ["google.api_core.gapic_v1.method._MethodDefault", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "_DEFAULT_VALUE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_inferred", "has_explicit_value"], "fullname": "google.api_core.gapic_v1.method._MethodDefault._DEFAULT_VALUE", "name": "_DEFAULT_VALUE", "setter_type": null, "type": "builtins.object"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.gapic_v1.method._MethodDefault.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.api_core.gapic_v1.method._MethodDefault", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.gapic_v1.method.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_apply_decorators": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["func", "decorators"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.gapic_v1.method._apply_decorators", "name": "_apply_decorators", "type": null}}, "_is_not_none_or_false": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.gapic_v1.method._is_not_none_or_false", "name": "_is_not_none_or_false", "type": null}}, "client_info": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1.client_info", "kind": "Gdef"}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "grpc_helpers": {".class": "SymbolTableNode", "cross_ref": "google.api_core.grpc_helpers", "kind": "Gdef"}, "wrap_method": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1, 1, 1, 5], "arg_names": ["func", "default_retry", "default_timeout", "default_compression", "client_info", "with_call"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.gapic_v1.method.wrap_method", "name": "wrap_method", "type": null}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\gapic_v1\\method.py"}