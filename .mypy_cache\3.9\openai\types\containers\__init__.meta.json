{"data_mtime": 1754400648, "dep_lines": [5, 6, 7, 8, 9, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.containers.file_list_params", "openai.types.containers.file_create_params", "openai.types.containers.file_list_response", "openai.types.containers.file_create_response", "openai.types.containers.file_retrieve_response", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "d00e4b37eef91a903f494752dd8c78a94b1b454b", "id": "openai.types.containers", "ignore_all": true, "interface_hash": "6ac05c7c0cdb569d172bd5c67d41ea30d57e7dae", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\containers\\__init__.py", "plugin_data": null, "size": 480, "suppressed": [], "version_id": "1.17.1"}