{"data_mtime": 1754400654, "dep_lines": [33, 34, 31, 26, 31, 22, 23, 23, 23, 24, 25, 42, 22, 25, 16, 17, 18, 19, 20, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 27, 28, 29], "dep_prios": [5, 5, 10, 5, 20, 10, 10, 10, 10, 10, 10, 10, 20, 20, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["google.ai.generativelanguage_v1beta.services.discuss_service.transports.base", "google.ai.generativelanguage_v1beta.services.discuss_service.transports.rest_base", "google.ai.generativelanguage_v1beta.types.discuss_service", "google.auth.transport.requests", "google.ai.generativelanguage_v1beta.types", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.rest_helpers", "google.api_core.rest_streaming", "google.api_core.retry", "google.auth.credentials", "google.api_core.client_logging", "google.api_core", "google.auth", "dataclasses", "json", "logging", "typing", "warnings", "builtins", "_frozen_importlib", "abc", "enum", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "google.auth.transport"], "hash": "7264cfb838161f5660f5dac5a76ec16ba6ae4e34", "id": "google.ai.generativelanguage_v1beta.services.discuss_service.transports.rest", "ignore_all": true, "interface_hash": "76d0a07944f035c39f36798f4d0928591eec06e4", "mtime": 1754241453, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\discuss_service\\transports\\rest.py", "plugin_data": null, "size": 36615, "suppressed": ["google.longrunning", "google.protobuf", "requests"], "version_id": "1.17.1"}