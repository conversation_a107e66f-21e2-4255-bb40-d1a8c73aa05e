{".class": "MypyFile", "_fullname": "openai.resources.beta", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Assistants": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.assistants.Assistants", "kind": "Gdef"}, "AssistantsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.assistants.AssistantsWithRawResponse", "kind": "Gdef"}, "AssistantsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.assistants.AssistantsWithStreamingResponse", "kind": "Gdef"}, "AsyncAssistants": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.assistants.AsyncAssistants", "kind": "Gdef"}, "AsyncAssistantsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.assistants.AsyncAssistantsWithRawResponse", "kind": "Gdef"}, "AsyncAssistantsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.assistants.AsyncAssistantsWithStreamingResponse", "kind": "Gdef"}, "AsyncBeta": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.AsyncBeta", "kind": "Gdef"}, "AsyncBetaWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.AsyncBetaWithRawResponse", "kind": "Gdef"}, "AsyncBetaWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.AsyncBetaWithStreamingResponse", "kind": "Gdef"}, "AsyncThreads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.AsyncThreads", "kind": "Gdef"}, "AsyncThreadsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse", "kind": "Gdef"}, "AsyncThreadsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse", "kind": "Gdef"}, "Beta": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.Beta", "kind": "Gdef"}, "BetaWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.BetaWithRawResponse", "kind": "Gdef"}, "BetaWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.beta.BetaWithStreamingResponse", "kind": "Gdef"}, "Threads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.Threads", "kind": "Gdef"}, "ThreadsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.ThreadsWithRawResponse", "kind": "Gdef"}, "ThreadsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.beta.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\__init__.py"}