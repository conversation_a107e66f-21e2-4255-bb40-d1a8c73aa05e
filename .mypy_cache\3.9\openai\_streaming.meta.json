{"data_mtime": 1754400649, "dep_lines": [12, 13, 16, 2, 4, 5, 6, 7, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 25, 5, 10, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai._utils", "openai._exceptions", "openai._client", "__future__", "json", "inspect", "types", "typing", "typing_extensions", "httpx", "builtins", "_frozen_importlib", "_typeshed", "abc", "httpx._models", "openai._base_client", "openai._legacy_response", "openai._models", "openai._response", "openai._types", "openai._utils._utils", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "c267cd1d28618e5dc9de8d9650653dc91a824b89", "id": "openai._streaming", "ignore_all": true, "interface_hash": "efde1957b11f637e4c85cc73fc0ac07f764b5f8c", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_streaming.py", "plugin_data": null, "size": 13390, "suppressed": [], "version_id": "1.17.1"}