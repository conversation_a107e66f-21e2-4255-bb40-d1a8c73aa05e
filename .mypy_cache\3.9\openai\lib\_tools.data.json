{".class": "MypyFile", "_fullname": "openai.lib._tools", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ChatCompletionToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "FunctionDefinition": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared_params.function_definition.FunctionDefinition", "kind": "Gdef"}, "PydanticFunctionTool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib._tools.PydanticFunctionTool", "name": "PydanticFunctionTool", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib._tools.PydanticFunctionTool", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai.lib._tools", "mro": ["openai.lib._tools.PydanticFunctionTool", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "defn", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib._tools.PydanticFunctionTool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "defn", "model"], "arg_types": ["openai.lib._tools.PydanticFunctionTool", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.function_definition.FunctionDefinition"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of PydanticFunctionTool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib._tools.PydanticFunctionTool.cast", "name": "cast", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib._tools.PydanticFunctionTool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cast of PydanticFunctionTool", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.function_definition.FunctionDefinition"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.lib._tools.PydanticFunctionTool.model", "name": "model", "setter_type": null, "type": {".class": "TypeType", "item": "pydantic.main.BaseModel"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib._tools.PydanticFunctionTool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.lib._tools.PydanticFunctionTool", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ResponsesFunctionToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.function_tool_param.FunctionToolParam", "kind": "Gdef"}, "ResponsesPydanticFunctionTool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib._tools.ResponsesPydanticFunctionTool", "name": "ResponsesPydanticFunctionTool", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib._tools.ResponsesPydanticFunctionTool", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "openai.lib._tools", "mro": ["openai.lib._tools.ResponsesPydanticFunctionTool", "builtins.dict", "typing.MutableMapping", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "tool", "model"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib._tools.ResponsesPydanticFunctionTool.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "tool", "model"], "arg_types": ["openai.lib._tools.ResponsesPydanticFunctionTool", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.function_tool_param.FunctionToolParam"}, {".class": "TypeType", "item": "pydantic.main.BaseModel"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ResponsesPydanticFunctionTool", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib._tools.ResponsesPydanticFunctionTool.cast", "name": "cast", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["openai.lib._tools.ResponsesPydanticFunctionTool"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "cast of ResponsesPydanticFunctionTool", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.function_tool_param.FunctionToolParam"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.lib._tools.ResponsesPydanticFunctionTool.model", "name": "model", "setter_type": null, "type": {".class": "TypeType", "item": "pydantic.main.BaseModel"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib._tools.ResponsesPydanticFunctionTool.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.lib._tools.ResponsesPydanticFunctionTool", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._tools.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._tools.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._tools.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._tools.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._tools.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._tools.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "pydantic": {".class": "SymbolTableNode", "cross_ref": "pydantic", "kind": "Gdef"}, "pydantic_function_tool": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["model", "name", "description"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._tools.pydantic_function_tool", "name": "pydantic_function_tool", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["model", "name", "description"], "arg_types": [{".class": "TypeType", "item": "pydantic.main.BaseModel"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pydantic_function_tool", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_strict_json_schema": {".class": "SymbolTableNode", "cross_ref": "openai.lib._pydantic.to_strict_json_schema", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\_tools.py"}