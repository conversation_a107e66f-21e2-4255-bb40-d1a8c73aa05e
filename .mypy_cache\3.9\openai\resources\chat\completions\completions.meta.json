{"data_mtime": 1754400651, "dep_lines": [14, 29, 29, 29, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 29, 36, 13, 22, 23, 24, 25, 26, 27, 28, 35, 3, 5, 6, 7, 8, 10, 11, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 10, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.chat.completions.messages", "openai.types.chat.completion_list_params", "openai.types.chat.completion_create_params", "openai.types.chat.completion_update_params", "openai.lib.streaming.chat", "openai.types.shared.chat_model", "openai.types.chat.chat_completion", "openai.types.shared_params.metadata", "openai.types.shared.reasoning_effort", "openai.types.chat.chat_completion_chunk", "openai.types.chat.parsed_chat_completion", "openai.types.chat.chat_completion_deleted", "openai.types.chat.chat_completion_tool_param", "openai.types.chat.chat_completion_audio_param", "openai.types.chat.chat_completion_message_param", "openai.types.chat.chat_completion_stream_options_param", "openai.types.chat.chat_completion_prediction_content_param", "openai.types.chat.chat_completion_tool_choice_option_param", "openai.types.chat", "openai.lib._parsing", "openai._legacy_response", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai._streaming", "openai.pagination", "openai._base_client", "__future__", "inspect", "typing", "functools", "typing_extensions", "httpx", "pydantic", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai._utils._utils", "openai.lib", "openai.lib.streaming", "openai.lib.streaming.chat._completions", "openai.types", "openai.types.chat.chat_completion_assistant_message_param", "openai.types.chat.chat_completion_content_part_image_param", "openai.types.chat.chat_completion_content_part_input_audio_param", "openai.types.chat.chat_completion_content_part_param", "openai.types.chat.chat_completion_content_part_refusal_param", "openai.types.chat.chat_completion_content_part_text_param", "openai.types.chat.chat_completion_developer_message_param", "openai.types.chat.chat_completion_function_call_option_param", "openai.types.chat.chat_completion_function_message_param", "openai.types.chat.chat_completion_message_tool_call_param", "openai.types.chat.chat_completion_named_tool_choice_param", "openai.types.chat.chat_completion_system_message_param", "openai.types.chat.chat_completion_tool_message_param", "openai.types.chat.chat_completion_user_message_param", "openai.types.shared_params", "openai.types.shared_params.function_definition", "openai.types.shared_params.response_format_json_object", "openai.types.shared_params.response_format_json_schema", "openai.types.shared_params.response_format_text", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "e021434a1ccafbe1adbde4e702b8c4a33fc63d3b", "id": "openai.resources.chat.completions.completions", "ignore_all": true, "interface_hash": "573af9d1ed3730210e6a9cd2a3c55819f8d780a6", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\chat\\completions\\completions.py", "plugin_data": null, "size": 157023, "suppressed": [], "version_id": "1.17.1"}