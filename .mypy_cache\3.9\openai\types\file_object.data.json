{".class": "MypyFile", "_fullname": "openai.types.file_object", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseModel": {".class": "SymbolTableNode", "cross_ref": "openai._models.BaseModel", "kind": "Gdef", "module_public": false}, "FileObject": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.file_object.FileObject", "name": "FileObject", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.file_object.FileObject", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 12, "name": "id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 15, "name": "bytes", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 18, "name": "created_at", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 21, "name": "filename", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 24, "name": "object", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "file"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 27, "name": "purpose", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "assistants"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "assistants_output"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch_output"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fine-tune"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fine-tune-results"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vision"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "user_data"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 43, "name": "status", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uploaded"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "processed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 50, "name": "expires_at", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 53, "name": "status_details", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.file_object", "mro": ["openai.types.file_object.FileObject", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.file_object.FileObject.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 5, 5], "arg_names": ["self", "_request_id", "id", "bytes", "created_at", "filename", "object", "purpose", "status", "expires_at", "status_details"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.file_object.FileObject.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 3, 3, 3, 5, 5], "arg_names": ["self", "_request_id", "id", "bytes", "created_at", "filename", "object", "purpose", "status", "expires_at", "status_details"], "arg_types": ["openai.types.file_object.FileObject", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.int", "builtins.int", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "file"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "assistants"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "assistants_output"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch_output"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fine-tune"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fine-tune-results"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vision"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "user_data"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uploaded"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "processed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of FileObject", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "bytes", "created_at", "filename", "object", "purpose", "status", "expires_at", "status_details"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.file_object.FileObject.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "bytes", "created_at", "filename", "object", "purpose", "status", "expires_at", "status_details"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.int", "builtins.int", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "file"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "assistants"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "assistants_output"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch_output"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fine-tune"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fine-tune-results"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vision"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "user_data"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uploaded"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "processed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of FileObject", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.file_object.FileObject.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "bytes", "created_at", "filename", "object", "purpose", "status", "expires_at", "status_details"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.int", "builtins.int", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "file"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "assistants"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "assistants_output"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch_output"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fine-tune"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fine-tune-results"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vision"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "user_data"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uploaded"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "processed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of FileObject", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "bytes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.file_object.FileObject.bytes", "name": "bytes", "setter_type": null, "type": "builtins.int"}}, "created_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.file_object.FileObject.created_at", "name": "created_at", "setter_type": null, "type": "builtins.int"}}, "expires_at": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.file_object.FileObject.expires_at", "name": "expires_at", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "filename": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.file_object.FileObject.filename", "name": "filename", "setter_type": null, "type": "builtins.str"}}, "id": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.file_object.FileObject.id", "name": "id", "setter_type": null, "type": "builtins.str"}}, "object": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.file_object.FileObject.object", "name": "object", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "file"}}}, "purpose": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.file_object.FileObject.purpose", "name": "purpose", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "assistants"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "assistants_output"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "batch_output"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fine-tune"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "fine-tune-results"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "vision"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "user_data"}], "uses_pep604_syntax": false}}}, "status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.file_object.FileObject.status", "name": "status", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "uploaded"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "processed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "error"}], "uses_pep604_syntax": false}}}, "status_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.file_object.FileObject.status_details", "name": "status_details", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.file_object.FileObject.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.file_object.FileObject", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.types.file_object.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.file_object.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.file_object.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.file_object.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.file_object.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.file_object.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.file_object.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\file_object.py"}