{".class": "MypyFile", "_fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "ListPermissionsAsyncPager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager", "name": "ListPermissionsAsyncPager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.ai.generativelanguage_v1beta.services.permission_service.pagers", "mro": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aiter__ of ListPermissionsAsyncPager", "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.permission.Permission"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattr__ of ListPermissionsAsyncPager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsRequest", "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse", {".class": "TypeAliasType", "args": [], "type_ref": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.OptionalAsyncRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ListPermissionsAsyncPager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of ListPermissionsAsyncPager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager._metadata", "name": "_metadata", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager._method", "name": "_method", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse"], "extra_attrs": null, "type_ref": "typing.Awaitable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager._request", "name": "_request", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsRequest"}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager._response", "name": "_response", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse"}}, "_retry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager._retry", "name": "_retry", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.OptionalAsyncRetry"}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager._timeout", "name": "_timeout", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}}}, "pages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_generator", "is_coroutine", "is_async_generator", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager.pages", "name": "pages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListPermissionsAsyncPager", "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager.pages", "name": "pages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListPermissionsAsyncPager", "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse"], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsAsyncPager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ListPermissionsPager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager", "name": "ListPermissionsPager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.ai.generativelanguage_v1beta.services.permission_service.pagers", "mro": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager", "builtins.object"], "names": {".class": "SymbolTable", "__getattr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": [null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager.__getattr__", "name": "__getattr__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": [null, null], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager", "builtins.str"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__getattr__ of ListPermissionsPager", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 5, 5, 5], "arg_names": ["self", "method", "request", "response", "retry", "timeout", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager", {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsRequest", "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse", {".class": "TypeAliasType", "args": [], "type_ref": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ListPermissionsPager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of ListPermissionsPager", "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.permission.Permission"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__repr__ of ListPermissionsPager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_metadata": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager._metadata", "name": "_metadata", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}}}, "_method": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager._method", "name": "_method", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": true, "name": null, "ret_type": "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager._request", "name": "_request", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsRequest"}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager._response", "name": "_response", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse"}}, "_retry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager._retry", "name": "_retry", "setter_type": null, "type": {".class": "TypeAliasType", "args": [], "type_ref": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.OptionalRetry"}}}, "_timeout": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager._timeout", "name": "_timeout", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.float", "builtins.object"], "uses_pep604_syntax": false}}}, "pages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_generator", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager.pages", "name": "pages", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListPermissionsPager", "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager.pages", "name": "pages", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pages of ListPermissionsPager", "ret_type": {".class": "Instance", "args": ["google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse"], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.ListPermissionsPager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "OptionalAsyncRetry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.OptionalAsyncRetry", "line": 34, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.api_core.retry.retry_unary_async.AsyncRetry", "google.api_core.gapic_v1.method._MethodDefault", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "OptionalRetry": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.OptionalRetry", "line": 33, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.api_core.retry.retry_unary.Retry", "google.api_core.gapic_v1.method._MethodDefault", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.permission_service.pagers.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef"}, "permission": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.permission", "kind": "Gdef"}, "permission_service": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.permission_service", "kind": "Gdef"}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry", "kind": "Gdef"}, "retries_async": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry_async", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\pagers.py"}