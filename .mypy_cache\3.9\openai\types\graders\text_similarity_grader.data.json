{".class": "MypyFile", "_fullname": "openai.types.graders.text_similarity_grader", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BaseModel": {".class": "SymbolTableNode", "cross_ref": "openai._models.BaseModel", "kind": "Gdef", "module_public": false}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Literal", "kind": "Gdef", "module_public": false}, "TextSimilarityGrader": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader", "name": "TextSimilarityGrader", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 11, "name": "evaluation_metric", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fuzzy_match"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bleu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gleu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "meteor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_l"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 20, "name": "input", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 23, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 26, "name": "reference", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 29, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "text_similarity"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.graders.text_similarity_grader", "mro": ["openai.types.graders.text_similarity_grader.TextSimilarityGrader", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "evaluation_metric", "input", "name", "reference", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "evaluation_metric", "input", "name", "reference", "type"], "arg_types": ["openai.types.graders.text_similarity_grader.TextSimilarityGrader", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fuzzy_match"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bleu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gleu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "meteor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_l"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "text_similarity"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TextSimilarityGrader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "evaluation_metric", "input", "name", "reference", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "evaluation_metric", "input", "name", "reference", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fuzzy_match"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bleu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gleu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "meteor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_l"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "text_similarity"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of TextSimilarityGrader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "evaluation_metric", "input", "name", "reference", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fuzzy_match"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bleu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gleu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "meteor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_l"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "text_similarity"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of TextSimilarityGrader", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "evaluation_metric": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader.evaluation_metric", "name": "evaluation_metric", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "fuzzy_match"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "bleu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "gleu"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "meteor"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_1"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_2"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_3"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_4"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_5"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "rouge_l"}], "uses_pep604_syntax": false}}}, "input": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader.input", "name": "input", "setter_type": null, "type": "builtins.str"}}, "name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "reference": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader.reference", "name": "reference", "setter_type": null, "type": "builtins.str"}}, "type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader.type", "name": "type", "setter_type": null, "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "text_similarity"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.graders.text_similarity_grader.TextSimilarityGrader.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.graders.text_similarity_grader.TextSimilarityGrader", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.types.graders.text_similarity_grader.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.text_similarity_grader.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.text_similarity_grader.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.text_similarity_grader.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.text_similarity_grader.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.text_similarity_grader.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.graders.text_similarity_grader.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\graders\\text_similarity_grader.py"}