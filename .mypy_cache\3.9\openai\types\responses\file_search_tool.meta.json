{"data_mtime": 1754400649, "dep_lines": [7, 8, 6, 3, 4, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.shared.compound_filter", "openai.types.shared.comparison_filter", "openai._models", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "openai.types.shared", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "d1621516b63602e8cb6066ff687d1379b168f39f", "id": "openai.types.responses.file_search_tool", "ignore_all": true, "interface_hash": "229ef5a2c2eabbf48d0a54732142e85f0c33df72", "mtime": 1754241437, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\file_search_tool.py", "plugin_data": null, "size": 1369, "suppressed": [], "version_id": "1.17.1"}