{".class": "MypyFile", "_fullname": "google.generativeai.types.safety_types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BlockedReason": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.safety_types.BlockedReason", "line": 44, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "google.ai.generativelanguage_v1beta.types.safety.ContentFilter.BlockedReason"}}, "ContentFilterDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.safety_types.ContentFilterDict", "name": "ContentFilterDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.ContentFilterDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.safety_types", "mro": ["google.generativeai.types.safety_types.ContentFilterDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["reason", "google.ai.generativelanguage_v1beta.types.safety.ContentFilter.BlockedReason"], ["message", "builtins.str"], ["__doc__", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}]], "readonly_keys": [], "required_keys": ["__doc__", "message", "reason"]}}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "EasySafetySetting": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.safety_types.EasySafetySetting", "line": 201, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmCategoryOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmBlockThresholdOptions"}], "extra_attrs": null, "type_ref": "typing.Mapping"}}}, "EasySafetySettingDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.safety_types.EasySafetySettingDict", "line": 202, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmCategoryOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmBlockThresholdOptions"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "HarmBlockThreshold": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.safety_types.HarmBlockThreshold", "line": 43, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "google.ai.generativelanguage_v1beta.types.safety.SafetySetting.HarmBlockThreshold"}}, "HarmBlockThresholdOptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.safety_types.HarmBlockThresholdOptions", "line": 110, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.int", "google.ai.generativelanguage_v1beta.types.safety.SafetySetting.HarmBlockThreshold"], "uses_pep604_syntax": false}}}, "HarmCategory": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.safety_types.HarmCategory", "name": "HarmCategory", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "google.generativeai.types.safety_types.HarmCategory", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.safety_types", "mro": ["google.generativeai.types.safety_types.HarmCategory", "builtins.object"], "names": {".class": "SymbolTable", "HARM_CATEGORY_DANGEROUS_CONTENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.safety_types.HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT", "name": "HARM_CATEGORY_DANGEROUS_CONTENT", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}}}, "HARM_CATEGORY_HARASSMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.safety_types.HarmCategory.HARM_CATEGORY_HARASSMENT", "name": "HARM_CATEGORY_HARASSMENT", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}}}, "HARM_CATEGORY_HATE_SPEECH": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.safety_types.HarmCategory.HARM_CATEGORY_HATE_SPEECH", "name": "HARM_CATEGORY_HATE_SPEECH", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}}}, "HARM_CATEGORY_SEXUALLY_EXPLICIT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.safety_types.HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT", "name": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}}}, "HARM_CATEGORY_UNSPECIFIED": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.safety_types.HarmCategory.HARM_CATEGORY_UNSPECIFIED", "name": "HARM_CATEGORY_UNSPECIFIED", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 5}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.safety_types.HarmCategory.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.safety_types.HarmCategory", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "HarmCategoryOptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.safety_types.HarmCategoryOptions", "line": 61, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.int", "google.generativeai.types.safety_types.HarmCategory"], "uses_pep604_syntax": false}}}, "HarmProbability": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.safety_types.HarmProbability", "line": 42, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "google.ai.generativelanguage_v1beta.types.safety.SafetyRating.HarmProbability"}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "LooseSafetySettingDict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.safety_types.LooseSafetySettingDict", "name": "LooseSafetySettingDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.LooseSafetySettingDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.safety_types", "mro": ["google.generativeai.types.safety_types.LooseSafetySettingDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["category", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmCategoryOptions"}], ["threshold", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmBlockThresholdOptions"}]], "readonly_keys": [], "required_keys": ["category", "threshold"]}}}, "Mapping": {".class": "SymbolTableNode", "cross_ref": "typing.Mapping", "kind": "Gdef", "module_public": false}, "SafetyFeedbackDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.safety_types.SafetyFeedbackDict", "name": "SafetyFeedbackDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.SafetyFeedbackDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.safety_types", "mro": ["google.generativeai.types.safety_types.SafetyFeedbackDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["rating", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetyRatingDict"}], ["setting", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingDict"}], ["__doc__", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}]], "readonly_keys": [], "required_keys": ["__doc__", "rating", "setting"]}}}, "SafetyRatingDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.safety_types.SafetyRatingDict", "name": "SafetyRatingDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.SafetyRatingDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.safety_types", "mro": ["google.generativeai.types.safety_types.SafetyRatingDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["category", "google.ai.generativelanguage_v1beta.types.safety.HarmCategory"], ["probability", "google.ai.generativelanguage_v1beta.types.safety.SafetyRating.HarmProbability"], ["__doc__", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}]], "readonly_keys": [], "required_keys": ["__doc__", "category", "probability"]}}}, "SafetySettingDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["typing._TypedDict"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.safety_types.SafetySettingDict", "name": "SafetySettingDict", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.SafetySettingDict", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.safety_types", "mro": ["google.generativeai.types.safety_types.SafetySettingDict", "typing._TypedDict", "typing.Mapping", "typing.Collection", "typing.Iterable", "typing.Container", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": {".class": "TypedDictType", "fallback": "typing._TypedDict", "items": [["category", "google.ai.generativelanguage_v1beta.types.safety.HarmCategory"], ["threshold", "google.ai.generativelanguage_v1beta.types.safety.SafetySetting.HarmBlockThreshold"], ["__doc__", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}]], "readonly_keys": [], "required_keys": ["__doc__", "category", "threshold"]}}}, "SafetySettingOptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.safety_types.SafetySettingOptions", "line": 204, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmBlockThresholdOptions"}, {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.EasySafetySetting"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.LooseSafetySettingDict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "TypedDict": {".class": "SymbolTableNode", "cross_ref": "typing.TypedDict", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_BLOCK_THRESHOLDS": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.safety_types._BLOCK_THRESHOLDS", "name": "_BLOCK_THRESHOLDS", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmBlockThresholdOptions"}, "google.ai.generativelanguage_v1beta.types.safety.SafetySetting.HarmBlockThreshold"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_HARM_CATEGORIES": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.safety_types._HARM_CATEGORIES", "name": "_HARM_CATEGORIES", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmCategoryOptions"}, "google.ai.generativelanguage_v1beta.types.safety.HarmCategory"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.safety_types.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.safety_types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.safety_types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.safety_types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.safety_types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.safety_types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.safety_types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_expand_block_threshold": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["block_threshold"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types._expand_block_threshold", "name": "_expand_block_threshold", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["block_threshold"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmBlockThresholdOptions"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_expand_block_threshold", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "convert_candidate_enums": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["candidates"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.convert_candidate_enums", "name": "convert_candidate_enums", "type": null}}, "convert_filters_to_enums": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filters"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.convert_filters_to_enums", "name": "convert_filters_to_enums", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filters"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_filters_to_enums", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.ContentFilterDict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_rating_to_enum": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["rating"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.convert_rating_to_enum", "name": "convert_rating_to_enum", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["rating"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_rating_to_enum", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetyRatingDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_ratings_to_enum": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ratings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.convert_ratings_to_enum", "name": "convert_ratings_to_enum", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ratings"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_ratings_to_enum", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetyRatingDict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_safety_feedback_to_enums": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["safety_feedback"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.convert_safety_feedback_to_enums", "name": "convert_safety_feedback_to_enums", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["safety_feedback"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Iterable"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_safety_feedback_to_enums", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetyFeedbackDict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "convert_setting_to_enum": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["setting"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.convert_setting_to_enum", "name": "convert_setting_to_enum", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["setting"], "arg_types": [{".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "convert_setting_to_enum", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "enum": {".class": "SymbolTableNode", "cross_ref": "enum", "kind": "Gdef", "module_public": false}, "normalize_safety_settings": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.normalize_safety_settings", "name": "normalize_safety_settings", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["settings"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "normalize_safety_settings", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingDict"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "proto": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.safety_types.proto", "name": "proto", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.generativeai.types.safety_types.proto", "source_any": null, "type_of_any": 3}}}, "protos": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.protos", "kind": "Gdef", "module_public": false}, "string_utils": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.string_utils", "kind": "Gdef", "module_public": false}, "to_block_threshold": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.to_block_threshold", "name": "to_block_threshold", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmBlockThresholdOptions"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_block_threshold", "ret_type": "google.ai.generativelanguage_v1beta.types.safety.SafetySetting.HarmBlockThreshold", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_easy_safety_dict": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["settings"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.to_easy_safety_dict", "name": "to_easy_safety_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["settings"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.SafetySettingOptions"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_easy_safety_dict", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.EasySafetySettingDict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_harm_category": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.safety_types.to_harm_category", "name": "to_harm_category", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.safety_types.HarmCategoryOptions"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_harm_category", "ret_type": "google.ai.generativelanguage_v1beta.types.safety.HarmCategory", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "typing": {".class": "SymbolTableNode", "cross_ref": "typing", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\safety_types.py"}