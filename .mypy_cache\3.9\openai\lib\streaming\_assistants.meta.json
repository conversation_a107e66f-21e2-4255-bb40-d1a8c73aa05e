{"data_mtime": 1754400650, "dep_lines": [25, 15, 14, 10, 11, 12, 13, 1, 3, 4, 5, 6, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.beta.threads.runs", "openai.types.beta.threads", "openai.types.beta", "openai._utils", "openai._compat", "openai._models", "openai._streaming", "__future__", "asyncio", "types", "typing", "typing_extensions", "httpx", "builtins", "_frozen_importlib", "abc", "asyncio.exceptions", "httpx._exceptions", "httpx._models", "openai.types", "openai.types.beta.assistant_stream_event", "openai.types.beta.threads.image_file", "openai.types.beta.threads.image_file_content_block", "openai.types.beta.threads.image_file_delta_block", "openai.types.beta.threads.image_url_content_block", "openai.types.beta.threads.image_url_delta_block", "openai.types.beta.threads.message", "openai.types.beta.threads.message_delta", "openai.types.beta.threads.message_delta_event", "openai.types.beta.threads.refusal_content_block", "openai.types.beta.threads.refusal_delta_block", "openai.types.beta.threads.run", "openai.types.beta.threads.runs.code_interpreter_tool_call", "openai.types.beta.threads.runs.code_interpreter_tool_call_delta", "openai.types.beta.threads.runs.file_search_tool_call", "openai.types.beta.threads.runs.file_search_tool_call_delta", "openai.types.beta.threads.runs.function_tool_call", "openai.types.beta.threads.runs.function_tool_call_delta", "openai.types.beta.threads.runs.message_creation_step_details", "openai.types.beta.threads.runs.run_step", "openai.types.beta.threads.runs.run_step_delta", "openai.types.beta.threads.runs.run_step_delta_event", "openai.types.beta.threads.runs.run_step_delta_message_delta", "openai.types.beta.threads.runs.tool_call_delta_object", "openai.types.beta.threads.runs.tool_calls_step_details", "openai.types.beta.threads.text", "openai.types.beta.threads.text_content_block", "openai.types.beta.threads.text_delta", "openai.types.beta.threads.text_delta_block", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "2328434d8a50f6d851a77f2838c8b34ee561c5f3", "id": "openai.lib.streaming._assistants", "ignore_all": true, "interface_hash": "f2b93dce34e246ef15454965acac2d410f5d8c96", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\_assistants.py", "plugin_data": null, "size": 40692, "suppressed": [], "version_id": "1.17.1"}