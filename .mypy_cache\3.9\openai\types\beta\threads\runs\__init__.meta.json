{"data_mtime": 1754400650, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.beta.threads.runs.run_step", "openai.types.beta.threads.runs.tool_call", "openai.types.beta.threads.runs.run_step_delta", "openai.types.beta.threads.runs.tool_call_delta", "openai.types.beta.threads.runs.run_step_include", "openai.types.beta.threads.runs.step_list_params", "openai.types.beta.threads.runs.function_tool_call", "openai.types.beta.threads.runs.run_step_delta_event", "openai.types.beta.threads.runs.step_retrieve_params", "openai.types.beta.threads.runs.code_interpreter_logs", "openai.types.beta.threads.runs.file_search_tool_call", "openai.types.beta.threads.runs.tool_call_delta_object", "openai.types.beta.threads.runs.tool_calls_step_details", "openai.types.beta.threads.runs.function_tool_call_delta", "openai.types.beta.threads.runs.code_interpreter_tool_call", "openai.types.beta.threads.runs.file_search_tool_call_delta", "openai.types.beta.threads.runs.run_step_delta_message_delta", "openai.types.beta.threads.runs.code_interpreter_output_image", "openai.types.beta.threads.runs.message_creation_step_details", "openai.types.beta.threads.runs.code_interpreter_tool_call_delta", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "de91c4fd0d6086d259b615a6523eaaa3300131b4", "id": "openai.types.beta.threads.runs", "ignore_all": true, "interface_hash": "c5d6979cde40282b0890fadd44338a24928c6960", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\threads\\runs\\__init__.py", "plugin_data": null, "size": 1653, "suppressed": [], "version_id": "1.17.1"}