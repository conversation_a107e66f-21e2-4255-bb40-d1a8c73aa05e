{"data_mtime": 1754400651, "dep_lines": [3, 11, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 30, 30, 30], "dependencies": ["openai.resources.evals.runs", "openai.resources.evals.evals", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "64b86787d62ed64358b43daacef12af8bac2761e", "id": "openai.resources.evals", "ignore_all": true, "interface_hash": "d01cebb143ba9556ea1cc06f3d09f7b80a296bc0", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\evals\\__init__.py", "plugin_data": null, "size": 771, "suppressed": [], "version_id": "1.17.1"}