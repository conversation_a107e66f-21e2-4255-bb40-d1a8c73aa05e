{".class": "MypyFile", "_fullname": "openai.types.beta.realtime", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ConversationCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_created_event.ConversationCreatedEvent", "kind": "Gdef"}, "ConversationItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item.ConversationItem", "kind": "Gdef"}, "ConversationItemContent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_content.ConversationItemContent", "kind": "Gdef"}, "ConversationItemContentParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_content_param.ConversationItemContentParam", "kind": "Gdef"}, "ConversationItemCreateEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_create_event.ConversationItemCreateEvent", "kind": "Gdef"}, "ConversationItemCreateEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_create_event_param.ConversationItemCreateEventParam", "kind": "Gdef"}, "ConversationItemCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_created_event.ConversationItemCreatedEvent", "kind": "Gdef"}, "ConversationItemDeleteEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_delete_event.ConversationItemDeleteEvent", "kind": "Gdef"}, "ConversationItemDeleteEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_delete_event_param.ConversationItemDeleteEventParam", "kind": "Gdef"}, "ConversationItemDeletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_deleted_event.ConversationItemDeletedEvent", "kind": "Gdef"}, "ConversationItemInputAudioTranscriptionCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_input_audio_transcription_completed_event.ConversationItemInputAudioTranscriptionCompletedEvent", "kind": "Gdef"}, "ConversationItemInputAudioTranscriptionDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_input_audio_transcription_delta_event.ConversationItemInputAudioTranscriptionDeltaEvent", "kind": "Gdef"}, "ConversationItemInputAudioTranscriptionFailedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_input_audio_transcription_failed_event.ConversationItemInputAudioTranscriptionFailedEvent", "kind": "Gdef"}, "ConversationItemParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_param.ConversationItemParam", "kind": "Gdef"}, "ConversationItemRetrieveEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_retrieve_event.ConversationItemRetrieveEvent", "kind": "Gdef"}, "ConversationItemRetrieveEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_retrieve_event_param.ConversationItemRetrieveEventParam", "kind": "Gdef"}, "ConversationItemTruncateEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_truncate_event.ConversationItemTruncateEvent", "kind": "Gdef"}, "ConversationItemTruncateEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_truncate_event_param.ConversationItemTruncateEventParam", "kind": "Gdef"}, "ConversationItemTruncatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_truncated_event.ConversationItemTruncatedEvent", "kind": "Gdef"}, "ConversationItemWithReference": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_with_reference.ConversationItemWithReference", "kind": "Gdef"}, "ConversationItemWithReferenceParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.conversation_item_with_reference_param.ConversationItemWithReferenceParam", "kind": "Gdef"}, "ErrorEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.error_event.ErrorEvent", "kind": "Gdef"}, "InputAudioBufferAppendEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_append_event.InputAudioBufferAppendEvent", "kind": "Gdef"}, "InputAudioBufferAppendEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_append_event_param.InputAudioBufferAppendEventParam", "kind": "Gdef"}, "InputAudioBufferClearEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_clear_event.InputAudioBufferClearEvent", "kind": "Gdef"}, "InputAudioBufferClearEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_clear_event_param.InputAudioBufferClearEventParam", "kind": "Gdef"}, "InputAudioBufferClearedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_cleared_event.InputAudioBufferClearedEvent", "kind": "Gdef"}, "InputAudioBufferCommitEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_commit_event.InputAudioBufferCommitEvent", "kind": "Gdef"}, "InputAudioBufferCommitEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_commit_event_param.InputAudioBufferCommitEventParam", "kind": "Gdef"}, "InputAudioBufferCommittedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_committed_event.InputAudioBufferCommittedEvent", "kind": "Gdef"}, "InputAudioBufferSpeechStartedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_speech_started_event.InputAudioBufferSpeechStartedEvent", "kind": "Gdef"}, "InputAudioBufferSpeechStoppedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.input_audio_buffer_speech_stopped_event.InputAudioBufferSpeechStoppedEvent", "kind": "Gdef"}, "RateLimitsUpdatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.rate_limits_updated_event.RateLimitsUpdatedEvent", "kind": "Gdef"}, "RealtimeClientEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_client_event.RealtimeClientEvent", "kind": "Gdef"}, "RealtimeClientEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_client_event_param.RealtimeClientEventParam", "kind": "Gdef"}, "RealtimeConnectParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_connect_params.RealtimeConnectParams", "kind": "Gdef"}, "RealtimeResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_response.RealtimeResponse", "kind": "Gdef"}, "RealtimeResponseStatus": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_response_status.RealtimeResponseStatus", "kind": "Gdef"}, "RealtimeResponseUsage": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_response_usage.RealtimeResponseUsage", "kind": "Gdef"}, "RealtimeServerEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.realtime_server_event.RealtimeServerEvent", "kind": "Gdef"}, "ResponseAudioDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_audio_delta_event.ResponseAudioDeltaEvent", "kind": "Gdef"}, "ResponseAudioDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_audio_done_event.ResponseAudioDoneEvent", "kind": "Gdef"}, "ResponseAudioTranscriptDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_audio_transcript_delta_event.ResponseAudioTranscriptDeltaEvent", "kind": "Gdef"}, "ResponseAudioTranscriptDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_audio_transcript_done_event.ResponseAudioTranscriptDoneEvent", "kind": "Gdef"}, "ResponseCancelEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_cancel_event.ResponseCancelEvent", "kind": "Gdef"}, "ResponseCancelEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_cancel_event_param.ResponseCancelEventParam", "kind": "Gdef"}, "ResponseContentPartAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_content_part_added_event.ResponseContentPartAddedEvent", "kind": "Gdef"}, "ResponseContentPartDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_content_part_done_event.ResponseContentPartDoneEvent", "kind": "Gdef"}, "ResponseCreateEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_create_event.ResponseCreateEvent", "kind": "Gdef"}, "ResponseCreateEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_create_event_param.ResponseCreateEventParam", "kind": "Gdef"}, "ResponseCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_created_event.ResponseCreatedEvent", "kind": "Gdef"}, "ResponseDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_done_event.ResponseDoneEvent", "kind": "Gdef"}, "ResponseFunctionCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_function_call_arguments_delta_event.ResponseFunctionCallArgumentsDeltaEvent", "kind": "Gdef"}, "ResponseFunctionCallArgumentsDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_function_call_arguments_done_event.ResponseFunctionCallArgumentsDoneEvent", "kind": "Gdef"}, "ResponseOutputItemAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_output_item_added_event.ResponseOutputItemAddedEvent", "kind": "Gdef"}, "ResponseOutputItemDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_output_item_done_event.ResponseOutputItemDoneEvent", "kind": "Gdef"}, "ResponseTextDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_text_delta_event.ResponseTextDeltaEvent", "kind": "Gdef"}, "ResponseTextDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.response_text_done_event.ResponseTextDoneEvent", "kind": "Gdef"}, "Session": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session.Session", "kind": "Gdef"}, "SessionCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session_create_params.SessionCreateParams", "kind": "Gdef"}, "SessionCreateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session_create_response.SessionCreateResponse", "kind": "Gdef"}, "SessionCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session_created_event.SessionCreatedEvent", "kind": "Gdef"}, "SessionUpdateEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session_update_event.SessionUpdateEvent", "kind": "Gdef"}, "SessionUpdateEventParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session_update_event_param.SessionUpdateEventParam", "kind": "Gdef"}, "SessionUpdatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.session_updated_event.SessionUpdatedEvent", "kind": "Gdef"}, "TranscriptionSession": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.transcription_session.TranscriptionSession", "kind": "Gdef"}, "TranscriptionSessionCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.transcription_session_create_params.TranscriptionSessionCreateParams", "kind": "Gdef"}, "TranscriptionSessionUpdate": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.transcription_session_update.TranscriptionSessionUpdate", "kind": "Gdef"}, "TranscriptionSessionUpdateParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.transcription_session_update_param.TranscriptionSessionUpdateParam", "kind": "Gdef"}, "TranscriptionSessionUpdatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.beta.realtime.transcription_session_updated_event.TranscriptionSessionUpdatedEvent", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.beta.realtime.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\__init__.py"}