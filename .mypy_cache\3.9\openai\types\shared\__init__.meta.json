{"data_mtime": 1754400649, "dep_lines": [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.shared.metadata", "openai.types.shared.reasoning", "openai.types.shared.all_models", "openai.types.shared.chat_model", "openai.types.shared.error_object", "openai.types.shared.compound_filter", "openai.types.shared.responses_model", "openai.types.shared.reasoning_effort", "openai.types.shared.comparison_filter", "openai.types.shared.function_definition", "openai.types.shared.function_parameters", "openai.types.shared.response_format_text", "openai.types.shared.response_format_json_object", "openai.types.shared.response_format_json_schema", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "b4e6235fbddd4cff876d5cd73a87eb1dc3e9d857", "id": "openai.types.shared", "ignore_all": true, "interface_hash": "0b2b6002337adedddf31ade84484686ed035aa77", "mtime": 1754241437, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\__init__.py", "plugin_data": null, "size": 991, "suppressed": [], "version_id": "1.17.1"}