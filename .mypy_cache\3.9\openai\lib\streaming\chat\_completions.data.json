{".class": "MypyFile", "_fullname": "openai.lib.streaming.chat._completions", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "AsyncChatCompletionStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "name": "AsyncChatCompletionStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming.chat._completions", "mro": ["openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}, "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aenter__ of AsyncChatCompletionStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}, "values": [], "variance": 0}]}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aexit__ of AsyncChatCompletionStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.__aiter__", "name": "__aiter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aiter__ of AsyncChatCompletionStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__anext__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.__anext__", "name": "__anext__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__anext__ of AsyncChatCompletionStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3], "arg_names": ["self", "raw_stream", "response_format", "input_tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3], "arg_names": ["self", "raw_stream", "response_format", "input_tools"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}, {".class": "Instance", "args": ["openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncChatCompletionStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__stream__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.__stream__", "name": "__stream__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__stream__ of AsyncChatCompletionStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iterator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream._iterator", "name": "_iterator", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "typing.AsyncIterator"}}}, "_raw_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream._raw_stream", "name": "_raw_stream", "setter_type": null, "type": {".class": "Instance", "args": ["openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream._response", "name": "_response", "setter_type": null, "type": "httpx._models.Response"}}, "_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream._state", "name": "_state", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of AsyncChatCompletionStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "current_completion_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.current_completion_snapshot", "name": "current_completion_snapshot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_completion_snapshot of AsyncChatCompletionStream", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.current_completion_snapshot", "name": "current_completion_snapshot", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_completion_snapshot of AsyncChatCompletionStream", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_final_completion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.get_final_completion", "name": "get_final_completion", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_final_completion of AsyncChatCompletionStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletion"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "until_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.until_done", "name": "until_done", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}, "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "until_done of AsyncChatCompletionStream", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}, "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ResponseFormatT"], "typeddict_type": null}}, "AsyncChatCompletionStreamManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "name": "AsyncChatCompletionStreamManager", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming.chat._completions", "mro": ["openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "builtins.object"], "names": {".class": "SymbolTable", "__aenter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager.__aenter__", "name": "__aenter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aenter__ of AsyncChatCompletionStreamManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__aexit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager.__aexit__", "name": "__aexit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "exc_type", "exc", "exc_tb"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__aexit__ of AsyncChatCompletionStreamManager", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__api_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager.__api_request", "name": "__api_request", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "api_request", "response_format", "input_tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "api_request", "response_format", "input_tools"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager"}, {".class": "Instance", "args": [{".class": "Instance", "args": ["openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "extra_attrs": null, "type_ref": "openai._streaming.AsyncStream"}], "extra_attrs": null, "type_ref": "typing.Awaitable"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of AsyncChatCompletionStreamManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__input_tools": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager.__input_tools", "name": "__input_tools", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}}}, "__response_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager.__response_format", "name": "__response_format", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}}}, "__stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager.__stream", "name": "__stream", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStream"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.AsyncChatCompletionStreamManager"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ResponseFormatT"], "typeddict_type": null}}, "AsyncIterator": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterator", "kind": "Gdef"}, "AsyncStream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.AsyncStream", "kind": "Gdef"}, "Awaitable": {".class": "SymbolTableNode", "cross_ref": "typing.Awaitable", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "ChatCompletionChunk": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_chunk.ChatCompletionChunk", "kind": "Gdef"}, "ChatCompletionStream": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream", "name": "ChatCompletionStream", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming.chat._completions", "mro": ["openai.lib.streaming.chat._completions.ChatCompletionStream", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}, "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of ChatCompletionStream", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}, "values": [], "variance": 0}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of ChatCompletionStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3], "arg_names": ["self", "raw_stream", "response_format", "input_tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3], "arg_names": ["self", "raw_stream", "response_format", "input_tools"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}, {".class": "Instance", "args": ["openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ChatCompletionStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.__iter__", "name": "__iter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__iter__ of ChatCompletionStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__next__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.__next__", "name": "__next__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__next__ of ChatCompletionStream", "ret_type": {".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__stream__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.__stream__", "name": "__stream__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__stream__ of ChatCompletionStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Iterator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_iterator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream._iterator", "name": "_iterator", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Iterator"}}}, "_raw_stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream._raw_stream", "name": "_raw_stream", "setter_type": null, "type": {".class": "Instance", "args": ["openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}}}, "_response": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream._response", "name": "_response", "setter_type": null, "type": "httpx._models.Response"}}, "_state": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream._state", "name": "_state", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState"}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.close", "name": "close", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "close of ChatCompletionStream", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "current_completion_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.current_completion_snapshot", "name": "current_completion_snapshot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_completion_snapshot of ChatCompletionStream", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.current_completion_snapshot", "name": "current_completion_snapshot", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_completion_snapshot of ChatCompletionStream", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_final_completion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.get_final_completion", "name": "get_final_completion", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_final_completion of ChatCompletionStream", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletion"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "until_done": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.until_done", "name": "until_done", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}, "values": [], "variance": 0}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "until_done of ChatCompletionStream", "ret_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}, "values": [], "variance": 0}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}, "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStream.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStream", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ResponseFormatT"], "typeddict_type": null}}, "ChatCompletionStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent", "kind": "Gdef"}, "ChatCompletionStreamManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "name": "ChatCompletionStreamManager", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming.chat._completions", "mro": ["openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "builtins.object"], "names": {".class": "SymbolTable", "__api_request": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager.__api_request", "name": "__api_request", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__enter__ of ChatCompletionStreamManager", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__exit__ of ChatCompletionStreamManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "api_request", "response_format", "input_tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 3], "arg_names": ["self", "api_request", "response_format", "input_tools"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager"}, {".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "Instance", "args": ["openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "extra_attrs": null, "type_ref": "openai._streaming.Stream"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ChatCompletionStreamManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__input_tools": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager.__input_tools", "name": "__input_tools", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": false}}}, "__response_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager.__response_format", "name": "__response_format", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}}}, "__stream": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager.__stream", "name": "__stream", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStream"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamManager"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ResponseFormatT"], "typeddict_type": null}}, "ChatCompletionStreamState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "name": "ChatCompletionStreamState", "type_vars": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming.chat._completions", "mro": ["openai.lib.streaming.chat._completions.ChatCompletionStreamState", "builtins.object"], "names": {".class": "SymbolTable", "__choice_event_states": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState.__choice_event_states", "name": "__choice_event_states", "setter_type": null, "type": {".class": "Instance", "args": ["openai.lib.streaming.chat._completions.ChoiceEventState"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__current_completion_snapshot": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState.__current_completion_snapshot", "name": "__current_completion_snapshot", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["self", "input_tools", "response_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["self", "input_tools", "response_format"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState"}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ChatCompletionStreamState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_accumulate_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState._accumulate_chunk", "name": "_accumulate_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState"}, "openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_accumulate_chunk of ChatCompletionStreamState", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "chunk", "completion_snapshot"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState._build_events", "name": "_build_events", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "chunk", "completion_snapshot"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState"}, "openai.types.chat.chat_completion_chunk.ChatCompletionChunk", {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_build_events of ChatCompletionStreamState", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_choice_state": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "choice"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState._get_choice_state", "name": "_get_choice_state", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "choice"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState"}, "openai.types.chat.chat_completion_chunk.Choice"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_choice_state of ChatCompletionStreamState", "ret_type": "openai.lib.streaming.chat._completions.ChoiceEventState", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_input_tools": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState._input_tools", "name": "_input_tools", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypedDictType", "fallback": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam", "items": [["function", {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared_params.function_definition.FunctionDefinition"}], ["type", {".class": "LiteralType", "fallback": "builtins.str", "value": "function"}]], "readonly_keys": [], "required_keys": ["function", "type"]}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_response_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState._response_format", "name": "_response_format", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}}}, "_rich_response_format": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState._rich_response_format", "name": "_rich_response_format", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.type", "openai._types.NotGiven"], "uses_pep604_syntax": true}}}, "current_completion_snapshot": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState.current_completion_snapshot", "name": "current_completion_snapshot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_completion_snapshot of ChatCompletionStreamState", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState.current_completion_snapshot", "name": "current_completion_snapshot", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "current_completion_snapshot of ChatCompletionStreamState", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_final_completion": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState.get_final_completion", "name": "get_final_completion", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_final_completion of ChatCompletionStreamState", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletion"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "handle_chunk": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState.handle_chunk", "name": "handle_chunk", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState"}, "openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "handle_chunk of ChatCompletionStreamState", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.ChatCompletionStreamState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": 1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChatCompletionStreamState", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.lib.streaming.chat._completions.ChatCompletionStreamState"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ResponseFormatT"], "typeddict_type": null}}, "ChatCompletionToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam", "kind": "Gdef"}, "ChoiceChunk": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_chunk.Choice", "kind": "Gdef"}, "ChoiceEventState": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState", "name": "ChoiceEventState", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "openai.lib.streaming.chat._completions", "mro": ["openai.lib.streaming.chat._completions.ChoiceEventState", "builtins.object"], "names": {".class": "SymbolTable", "__current_tool_call_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState.__current_tool_call_index", "name": "__current_tool_call_index", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": ["self", "input_tools"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": ["self", "input_tools"], "arg_types": ["openai.lib.streaming.chat._completions.ChoiceEventState", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "builtins.list"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ChoiceEventState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_add_tool_done_event": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3], "arg_names": ["self", "events_to_fire", "choice_snapshot", "tool_index"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState._add_tool_done_event", "name": "_add_tool_done_event", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3], "arg_names": ["self", "events_to_fire", "choice_snapshot", "tool_index"], "arg_types": ["openai.lib.streaming.chat._completions.ChoiceEventState", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChoiceEventState._add_tool_done_event", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChoiceSnapshot"}, "builtins.int"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_add_tool_done_event of ChoiceEventState", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChoiceEventState._add_tool_done_event", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_content_done": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState._content_done", "name": "_content_done", "setter_type": null, "type": "builtins.bool"}}, "_content_done_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "choice_snapshot", "response_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState._content_done_events", "name": "_content_done_events", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "choice_snapshot", "response_format"], "arg_types": ["openai.lib.streaming.chat._completions.ChoiceEventState", {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChoiceSnapshot"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChoiceEventState._content_done_events", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_content_done_events of ChoiceEventState", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChoiceEventState._content_done_events", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChoiceEventState._content_done_events", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "_done_tool_calls": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState._done_tool_calls", "name": "_done_tool_calls", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "_input_tools": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState._input_tools", "name": "_input_tools", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_logprobs_content_done": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState._logprobs_content_done", "name": "_logprobs_content_done", "setter_type": null, "type": "builtins.bool"}}, "_logprobs_refusal_done": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState._logprobs_refusal_done", "name": "_logprobs_refusal_done", "setter_type": null, "type": "builtins.bool"}}, "_refusal_done": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState._refusal_done", "name": "_refusal_done", "setter_type": null, "type": "builtins.bool"}}, "get_done_events": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3, 3], "arg_names": ["self", "choice_chunk", "choice_snapshot", "response_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState.get_done_events", "name": "get_done_events", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3, 3], "arg_names": ["self", "choice_chunk", "choice_snapshot", "response_format"], "arg_types": ["openai.lib.streaming.chat._completions.ChoiceEventState", "openai.types.chat.chat_completion_chunk.Choice", {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChoiceSnapshot"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChoiceEventState.get_done_events", "upper_bound": "builtins.object", "values": [], "variance": 0}}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.chat.completion_create_params.ResponseFormat"}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_done_events of ChoiceEventState", "ret_type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChoiceEventState.get_done_events", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.lib.streaming.chat._events.ChatCompletionStreamEvent"}], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._completions.ResponseFormatT", "id": -1, "name": "ResponseFormatT", "namespace": "openai.lib.streaming.chat._completions.ChoiceEventState.get_done_events", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.lib.streaming.chat._completions.ChoiceEventState.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.lib.streaming.chat._completions.ChoiceEventState", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChoiceLogprobs": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion.ChoiceLogprobs", "kind": "Gdef"}, "ChunkEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.ChunkEvent", "kind": "Gdef"}, "ContentDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.ContentDeltaEvent", "kind": "Gdef"}, "ContentDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.ContentDoneEvent", "kind": "Gdef"}, "ContentFilterFinishReasonError": {".class": "SymbolTableNode", "cross_ref": "openai._exceptions.ContentFilterFinishReasonError", "kind": "Gdef"}, "FunctionToolCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.FunctionToolCallArgumentsDeltaEvent", "kind": "Gdef"}, "FunctionToolCallArgumentsDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.FunctionToolCallArgumentsDoneEvent", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "IncEx": {".class": "SymbolTableNode", "cross_ref": "openai._types.IncEx", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Iterator": {".class": "SymbolTableNode", "cross_ref": "typing.Iterator", "kind": "Gdef"}, "LengthFinishReasonError": {".class": "SymbolTableNode", "cross_ref": "openai._exceptions.LengthFinishReasonError", "kind": "Gdef"}, "LogprobsContentDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.LogprobsContentDeltaEvent", "kind": "Gdef"}, "LogprobsContentDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.LogprobsContentDoneEvent", "kind": "Gdef"}, "LogprobsRefusalDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.LogprobsRefusalDeltaEvent", "kind": "Gdef"}, "LogprobsRefusalDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.LogprobsRefusalDoneEvent", "kind": "Gdef"}, "NOT_GIVEN": {".class": "SymbolTableNode", "cross_ref": "openai._types.NOT_GIVEN", "kind": "Gdef"}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef"}, "ParsedChatCompletion": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletion", "kind": "Gdef"}, "ParsedChatCompletionMessageSnapshot": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionMessageSnapshot", "kind": "Gdef"}, "ParsedChatCompletionSnapshot": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot", "kind": "Gdef"}, "ParsedChoiceSnapshot": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._types.ParsedChoiceSnapshot", "kind": "Gdef"}, "RefusalDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.RefusalDeltaEvent", "kind": "Gdef"}, "RefusalDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming.chat._events.RefusalDoneEvent", "kind": "Gdef"}, "ResponseFormatParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.completion_create_params.ResponseFormat", "kind": "Gdef"}, "ResponseFormatT": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.ResponseFormatT", "kind": "Gdef"}, "Self": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Self", "kind": "Gdef"}, "Stream": {".class": "SymbolTableNode", "cross_ref": "openai._streaming.Stream", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TracebackType": {".class": "SymbolTableNode", "cross_ref": "types.TracebackType", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._completions.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._completions.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._completions.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._completions.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._completions.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib.streaming.chat._completions.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "_convert_initial_chunk_into_snapshot": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["chunk"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.chat._completions._convert_initial_chunk_into_snapshot", "name": "_convert_initial_chunk_into_snapshot", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["chunk"], "arg_types": ["openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_convert_initial_chunk_into_snapshot", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.lib.streaming.chat._types.ParsedChatCompletionSnapshot"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_valid_chat_completion_chunk_weak": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["sse_event"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib.streaming.chat._completions._is_valid_chat_completion_chunk_weak", "name": "_is_valid_chat_completion_chunk_weak", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["sse_event"], "arg_types": ["openai.types.chat.chat_completion_chunk.ChatCompletionChunk"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_is_valid_chat_completion_chunk_weak", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "accumulate_delta": {".class": "SymbolTableNode", "cross_ref": "openai.lib.streaming._deltas.accumulate_delta", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.assert_never", "kind": "Gdef"}, "build": {".class": "SymbolTableNode", "cross_ref": "openai._models.build", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "construct_type": {".class": "SymbolTableNode", "cross_ref": "openai._models.construct_type", "kind": "Gdef"}, "consume_async_iterator": {".class": "SymbolTableNode", "cross_ref": "openai._utils._streams.consume_async_iterator", "kind": "Gdef"}, "consume_sync_iterator": {".class": "SymbolTableNode", "cross_ref": "openai._utils._streams.consume_sync_iterator", "kind": "Gdef"}, "from_json": {".class": "SymbolTableNode", "cross_ref": "jiter.from_json", "kind": "Gdef"}, "get_input_tool_by_name": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.get_input_tool_by_name", "kind": "Gdef"}, "has_parseable_input": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.has_parseable_input", "kind": "Gdef"}, "inspect": {".class": "SymbolTableNode", "cross_ref": "inspect", "kind": "Gdef"}, "is_given": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_given", "kind": "Gdef"}, "maybe_parse_content": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.maybe_parse_content", "kind": "Gdef"}, "model_dump": {".class": "SymbolTableNode", "cross_ref": "openai._compat.model_dump", "kind": "Gdef"}, "parse_chat_completion": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.parse_chat_completion", "kind": "Gdef"}, "parse_function_tool_arguments": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.parse_function_tool_arguments", "kind": "Gdef"}, "solve_response_format_t": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.solve_response_format_t", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\chat\\_completions.py"}