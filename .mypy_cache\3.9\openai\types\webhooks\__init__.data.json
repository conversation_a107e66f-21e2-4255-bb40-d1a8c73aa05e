{".class": "MypyFile", "_fullname": "openai.types.webhooks", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "BatchCancelledWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.batch_cancelled_webhook_event.BatchCancelledWebhookEvent", "kind": "Gdef"}, "BatchCompletedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.batch_completed_webhook_event.BatchCompletedWebhookEvent", "kind": "Gdef"}, "BatchExpiredWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.batch_expired_webhook_event.BatchExpiredWebhookEvent", "kind": "Gdef"}, "BatchFailedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.batch_failed_webhook_event.BatchFailedWebhookEvent", "kind": "Gdef"}, "EvalRunCanceledWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.eval_run_canceled_webhook_event.EvalRunCanceledWebhookEvent", "kind": "Gdef"}, "EvalRunFailedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.eval_run_failed_webhook_event.EvalRunFailedWebhookEvent", "kind": "Gdef"}, "EvalRunSucceededWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.eval_run_succeeded_webhook_event.EvalRunSucceededWebhookEvent", "kind": "Gdef"}, "FineTuningJobCancelledWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.fine_tuning_job_cancelled_webhook_event.FineTuningJobCancelledWebhookEvent", "kind": "Gdef"}, "FineTuningJobFailedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.fine_tuning_job_failed_webhook_event.FineTuningJobFailedWebhookEvent", "kind": "Gdef"}, "FineTuningJobSucceededWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.fine_tuning_job_succeeded_webhook_event.FineTuningJobSucceededWebhookEvent", "kind": "Gdef"}, "ResponseCancelledWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.response_cancelled_webhook_event.ResponseCancelledWebhookEvent", "kind": "Gdef"}, "ResponseCompletedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.response_completed_webhook_event.ResponseCompletedWebhookEvent", "kind": "Gdef"}, "ResponseFailedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.response_failed_webhook_event.ResponseFailedWebhookEvent", "kind": "Gdef"}, "ResponseIncompleteWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.response_incomplete_webhook_event.ResponseIncompleteWebhookEvent", "kind": "Gdef"}, "UnwrapWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.unwrap_webhook_event.UnwrapWebhookEvent", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\__init__.py"}