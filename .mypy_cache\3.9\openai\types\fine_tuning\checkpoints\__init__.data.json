{".class": "MypyFile", "_fullname": "openai.types.fine_tuning.checkpoints", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "PermissionCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.checkpoints.permission_create_params.PermissionCreateParams", "kind": "Gdef"}, "PermissionCreateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.checkpoints.permission_create_response.PermissionCreateResponse", "kind": "Gdef"}, "PermissionDeleteResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.checkpoints.permission_delete_response.PermissionDeleteResponse", "kind": "Gdef"}, "PermissionRetrieveParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.checkpoints.permission_retrieve_params.PermissionRetrieveParams", "kind": "Gdef"}, "PermissionRetrieveResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.fine_tuning.checkpoints.permission_retrieve_response.PermissionRetrieveResponse", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.checkpoints.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.checkpoints.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.checkpoints.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.checkpoints.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.checkpoints.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.checkpoints.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.fine_tuning.checkpoints.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\fine_tuning\\checkpoints\\__init__.py"}