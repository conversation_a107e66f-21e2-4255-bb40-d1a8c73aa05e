{".class": "MypyFile", "_fullname": "openai.types.shared", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AllModels": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.all_models.AllModels", "kind": "Gdef"}, "ChatModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.chat_model.ChatModel", "kind": "Gdef"}, "ComparisonFilter": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.comparison_filter.ComparisonFilter", "kind": "Gdef"}, "CompoundFilter": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.compound_filter.CompoundFilter", "kind": "Gdef"}, "ErrorObject": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.error_object.ErrorObject", "kind": "Gdef"}, "FunctionDefinition": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.function_definition.FunctionDefinition", "kind": "Gdef"}, "FunctionParameters": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.function_parameters.FunctionParameters", "kind": "Gdef"}, "Metadata": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.metadata.Metadata", "kind": "Gdef"}, "Reasoning": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.reasoning.Reasoning", "kind": "Gdef"}, "ReasoningEffort": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.reasoning_effort.ReasoningEffort", "kind": "Gdef"}, "ResponseFormatJSONObject": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.response_format_json_object.ResponseFormatJSONObject", "kind": "Gdef"}, "ResponseFormatJSONSchema": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.response_format_json_schema.ResponseFormatJSONSchema", "kind": "Gdef"}, "ResponseFormatText": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.response_format_text.ResponseFormatText", "kind": "Gdef"}, "ResponsesModel": {".class": "SymbolTableNode", "cross_ref": "openai.types.shared.responses_model.ResponsesModel", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.shared.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.shared.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.shared.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.shared.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.shared.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.shared.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.shared.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\shared\\__init__.py"}