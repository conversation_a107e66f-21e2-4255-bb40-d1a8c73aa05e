{".class": "MypyFile", "_fullname": "openai.types.chat", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ChatCompletion": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion.ChatCompletion", "kind": "Gdef"}, "ChatCompletionAssistantMessageParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_assistant_message_param.ChatCompletionAssistantMessageParam", "kind": "Gdef"}, "ChatCompletionAudio": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_audio.ChatCompletionAudio", "kind": "Gdef"}, "ChatCompletionAudioParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_audio_param.ChatCompletionAudioParam", "kind": "Gdef"}, "ChatCompletionChunk": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_chunk.ChatCompletionChunk", "kind": "Gdef"}, "ChatCompletionContentPartImage": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_content_part_image.ChatCompletionContentPartImage", "kind": "Gdef"}, "ChatCompletionContentPartImageParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_content_part_image_param.ChatCompletionContentPartImageParam", "kind": "Gdef"}, "ChatCompletionContentPartInputAudioParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_content_part_input_audio_param.ChatCompletionContentPartInputAudioParam", "kind": "Gdef"}, "ChatCompletionContentPartParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_content_part_param.ChatCompletionContentPartParam", "kind": "Gdef"}, "ChatCompletionContentPartRefusalParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_content_part_refusal_param.ChatCompletionContentPartRefusalParam", "kind": "Gdef"}, "ChatCompletionContentPartText": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_content_part_text.ChatCompletionContentPartText", "kind": "Gdef"}, "ChatCompletionContentPartTextParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_content_part_text_param.ChatCompletionContentPartTextParam", "kind": "Gdef"}, "ChatCompletionDeleted": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_deleted.ChatCompletionDeleted", "kind": "Gdef"}, "ChatCompletionDeveloperMessageParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_developer_message_param.ChatCompletionDeveloperMessageParam", "kind": "Gdef"}, "ChatCompletionFunctionCallOptionParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_function_call_option_param.ChatCompletionFunctionCallOptionParam", "kind": "Gdef"}, "ChatCompletionFunctionMessageParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_function_message_param.ChatCompletionFunctionMessageParam", "kind": "Gdef"}, "ChatCompletionMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_message.ChatCompletionMessage", "kind": "Gdef"}, "ChatCompletionMessageParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_message_param.ChatCompletionMessageParam", "kind": "Gdef"}, "ChatCompletionMessageToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_message_tool_call.ChatCompletionMessageToolCall", "kind": "Gdef"}, "ChatCompletionMessageToolCallParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_message_tool_call_param.ChatCompletionMessageToolCallParam", "kind": "Gdef"}, "ChatCompletionModality": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_modality.ChatCompletionModality", "kind": "Gdef"}, "ChatCompletionNamedToolChoiceParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_named_tool_choice_param.ChatCompletionNamedToolChoiceParam", "kind": "Gdef"}, "ChatCompletionPredictionContentParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_prediction_content_param.ChatCompletionPredictionContentParam", "kind": "Gdef"}, "ChatCompletionReasoningEffort": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_reasoning_effort.ChatCompletionReasoningEffort", "kind": "Gdef"}, "ChatCompletionRole": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_role.ChatCompletionRole", "kind": "Gdef"}, "ChatCompletionStoreMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_store_message.ChatCompletionStoreMessage", "kind": "Gdef"}, "ChatCompletionStreamOptionsParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_stream_options_param.ChatCompletionStreamOptionsParam", "kind": "Gdef"}, "ChatCompletionSystemMessageParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_system_message_param.ChatCompletionSystemMessageParam", "kind": "Gdef"}, "ChatCompletionTokenLogprob": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_token_logprob.ChatCompletionTokenLogprob", "kind": "Gdef"}, "ChatCompletionTool": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_tool.ChatCompletionTool", "kind": "Gdef"}, "ChatCompletionToolChoiceOptionParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_tool_choice_option_param.ChatCompletionToolChoiceOptionParam", "kind": "Gdef"}, "ChatCompletionToolMessageParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_tool_message_param.ChatCompletionToolMessageParam", "kind": "Gdef"}, "ChatCompletionToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_tool_param.ChatCompletionToolParam", "kind": "Gdef"}, "ChatCompletionUserMessageParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.chat_completion_user_message_param.ChatCompletionUserMessageParam", "kind": "Gdef"}, "CompletionCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.completion_create_params.CompletionCreateParams", "kind": "Gdef"}, "CompletionListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.completion_list_params.CompletionListParams", "kind": "Gdef"}, "CompletionUpdateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.completion_update_params.CompletionUpdateParams", "kind": "Gdef"}, "ParsedChatCompletion": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletion", "kind": "Gdef"}, "ParsedChatCompletionMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_chat_completion.ParsedChatCompletionMessage", "kind": "Gdef"}, "ParsedChoice": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_chat_completion.ParsedChoice", "kind": "Gdef"}, "ParsedFunction": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_function_tool_call.ParsedFunction", "kind": "Gdef"}, "ParsedFunctionToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.parsed_function_tool_call.ParsedFunctionToolCall", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.chat.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.chat.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.chat.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.chat.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.chat.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.chat.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.chat.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\chat\\__init__.py"}