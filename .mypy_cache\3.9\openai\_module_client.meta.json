{"data_mtime": 1754400651, "dep_lines": [14, 15, 17, 19, 21, 22, 23, 24, 25, 9, 10, 11, 12, 13, 16, 18, 20, 28, 3, 5, 6, 27, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 25, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.beta.beta", "openai.resources.chat.chat", "openai.resources.audio.audio", "openai.resources.evals.evals", "openai.resources.uploads.uploads", "openai.resources.responses.responses", "openai.resources.containers.containers", "openai.resources.fine_tuning.fine_tuning", "openai.resources.vector_stores.vector_stores", "openai.resources.files", "openai.resources.images", "openai.resources.models", "openai.resources.batches", "openai.resources.webhooks", "openai.resources.embeddings", "openai.resources.completions", "openai.resources.moderations", "openai._utils", "__future__", "typing", "typing_extensions", "openai", "builtins", "_frozen_importlib", "abc", "openai._resource", "openai._utils._proxy", "openai.resources", "openai.resources.audio", "openai.resources.beta", "openai.resources.chat", "openai.resources.containers", "openai.resources.evals", "openai.resources.fine_tuning", "openai.resources.responses", "openai.resources.uploads", "openai.resources.vector_stores"], "hash": "97ab0f3db9549fadac4f0c8cd761e389426a8f7d", "id": "openai._module_client", "ignore_all": true, "interface_hash": "9c5171ee1c6a8fc0be345f7038da9290491db304", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_module_client.py", "plugin_data": null, "size": 4281, "suppressed": [], "version_id": "1.17.1"}