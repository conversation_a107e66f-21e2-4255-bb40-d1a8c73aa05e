{"data_mtime": 1754400649, "dep_lines": [10, 11, 12, 13, 14, 16, 15, 17, 8, 9, 3, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.shared.metadata", "openai.types.graders.python_grader", "openai.types.graders.label_model_grader", "openai.types.graders.score_model_grader", "openai.types.graders.string_check_grader", "openai.types.graders.text_similarity_grader", "openai.types.eval_custom_data_source_config", "openai.types.eval_stored_completions_data_source_config", "openai._utils", "openai._models", "typing", "typing_extensions", "pydantic", "builtins", "_frozen_importlib", "abc", "annotated_types", "openai.types.graders", "pydantic._internal", "pydantic._internal._model_construction", "pydantic._internal._repr", "pydantic.aliases", "pydantic.fields", "pydantic.main", "pydantic.types", "re"], "hash": "7c169ab5ddc767cb101f5fb2c11377f7aea251ff", "id": "openai.types.eval_list_response", "ignore_all": true, "interface_hash": "aa0635dbad77b5b2e746f5c8fda63d63ebb30129", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\eval_list_response.py", "plugin_data": null, "size": 3527, "suppressed": [], "version_id": "1.17.1"}