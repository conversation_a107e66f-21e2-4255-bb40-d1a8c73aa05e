{"data_mtime": 1754400650, "dep_lines": [19, 22, 24, 25, 26, 27, 19, 21, 23, 11, 12, 13, 14, 15, 16, 17, 18, 20, 3, 5, 6, 7, 9, 11, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 5, 5, 5, 5, 5, 20, 5, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.audio.transcription_create_params", "openai.types.audio.transcription", "openai.types.audio.transcription_include", "openai.types.audio.transcription_verbose", "openai.types.audio.transcription_stream_event", "openai.types.audio.transcription_create_response", "openai.types.audio", "openai.types.audio_model", "openai.types.audio_response_format", "openai._legacy_response", "openai.types", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai._streaming", "openai._base_client", "__future__", "logging", "typing", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai._utils._utils", "openai.types.audio.transcription_text_delta_event", "openai.types.audio.transcription_text_done_event", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "a1f6d70812619476edb18e377a4e2b2e939935c6", "id": "openai.resources.audio.transcriptions", "ignore_all": true, "interface_hash": "86365f1af233d31d913c9b98be2e930b9de1ef83", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\audio\\transcriptions.py", "plugin_data": null, "size": 39960, "suppressed": [], "version_id": "1.17.1"}