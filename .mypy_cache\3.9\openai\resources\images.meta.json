{"data_mtime": 1754400650, "dep_lines": [11, 11, 11, 19, 20, 21, 22, 10, 11, 12, 13, 14, 15, 16, 17, 18, 3, 5, 6, 8, 10, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 5, 5, 5, 5, 10, 20, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.image_edit_params", "openai.types.image_generate_params", "openai.types.image_create_variation_params", "openai.types.image_model", "openai.types.images_response", "openai.types.image_gen_stream_event", "openai.types.image_edit_stream_event", "openai._legacy_response", "openai.types", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai._streaming", "openai._base_client", "__future__", "typing", "typing_extensions", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai._utils._utils", "openai.types.image_edit_completed_event", "openai.types.image_edit_partial_image_event", "openai.types.image_gen_completed_event", "openai.types.image_gen_partial_image_event", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "41c270a6d816e959f04eb12c053af0573de8bf65", "id": "openai.resources.images", "ignore_all": true, "interface_hash": "62b0173fedc84055d73b7d6254905170da1fac43", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\images.py", "plugin_data": null, "size": 97201, "suppressed": [], "version_id": "1.17.1"}