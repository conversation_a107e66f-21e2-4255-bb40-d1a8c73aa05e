{"data_mtime": 1754400654, "dep_lines": [40, 41, 36, 38, 27, 36, 23, 24, 24, 25, 26, 44, 23, 26, 16, 17, 18, 19, 20, 21, 31, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 30, 31, 28, 29, 33, 32, 34], "dep_prios": [5, 5, 10, 10, 5, 20, 10, 10, 10, 10, 10, 10, 20, 20, 10, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 10, 5, 5, 5, 10, 10], "dependencies": ["google.ai.generativelanguage_v1beta.services.permission_service.transports.base", "google.ai.generativelanguage_v1beta.services.permission_service.transports.grpc", "google.ai.generativelanguage_v1beta.types.permission", "google.ai.generativelanguage_v1beta.types.permission_service", "google.auth.transport.grpc", "google.ai.generativelanguage_v1beta.types", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.grpc_helpers_async", "google.api_core.retry_async", "google.auth.credentials", "google.api_core.client_logging", "google.api_core", "google.auth", "inspect", "json", "logging", "pickle", "typing", "warnings", "google", "builtins", "_frozen_importlib", "_warnings", "abc", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method_async", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary_async", "google.auth._credentials_base", "google.auth.transport", "types"], "hash": "42f966432d3f16b26b587701bdbf0bb578924d27", "id": "google.ai.generativelanguage_v1beta.services.permission_service.transports.grpc_asyncio", "ignore_all": true, "interface_hash": "28d60ff451d6cfb5356bdc8c3328a0a6480f31fd", "mtime": 1754241453, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\permission_service\\transports\\grpc_asyncio.py", "plugin_data": null, "size": 28308, "suppressed": ["google.protobuf.json_format", "google.protobuf.message", "google.longrunning", "google.protobuf", "grpc.experimental", "grpc", "proto"], "version_id": "1.17.1"}