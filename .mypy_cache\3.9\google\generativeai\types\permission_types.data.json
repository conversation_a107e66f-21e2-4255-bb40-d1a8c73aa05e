{".class": "MypyFile", "_fullname": "google.generativeai.types.permission_types", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AsyncIterable": {".class": "SymbolTableNode", "cross_ref": "typing.AsyncIterable", "kind": "Gdef", "module_public": false}, "GranteeType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.permission_types.GranteeType", "line": 33, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "google.ai.generativelanguage_v1beta.types.permission.Permission.GranteeType"}}, "GranteeTypeOptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.permission_types.GranteeTypeOptions", "line": 36, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.int", "google.ai.generativelanguage_v1beta.types.permission.Permission.GranteeType"], "uses_pep604_syntax": false}}}, "INVALID_PERMISSION_ID_MSG": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.permission_types.INVALID_PERMISSION_ID_MSG", "name": "INVALID_PERMISSION_ID_MSG", "setter_type": null, "type": "builtins.str"}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "Permission": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.permission_types.Permission", "name": "Permission", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.permission_types.Permission", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 99, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 100, "name": "role", "type": "google.ai.generativelanguage_v1beta.types.permission.Permission.Role"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 101, "name": "grantee_type", "type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.permission.Permission.GranteeType", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 102, "name": "email_address", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "google.generativeai.types.permission_types", "mro": ["google.generativeai.types.permission_types.Permission", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "google.generativeai.types.permission_types.Permission.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "name", "role", "grantee_type", "email_address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permission.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1, 1], "arg_names": ["self", "name", "role", "grantee_type", "email_address"], "arg_types": ["google.generativeai.types.permission_types.Permission", "builtins.str", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.RoleOptions"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.GranteeTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of Permission", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5], "arg_names": ["name", "role", "grantee_type", "email_address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.generativeai.types.permission_types.Permission.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["name", "role", "grantee_type", "email_address"], "arg_types": ["builtins.str", "google.ai.generativelanguage_v1beta.types.permission.Permission.Role", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.permission.Permission.GranteeType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Permission", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "google.generativeai.types.permission_types.Permission.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5], "arg_names": ["name", "role", "grantee_type", "email_address"], "arg_types": ["builtins.str", "google.ai.generativelanguage_v1beta.types.permission.Permission.Role", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.permission.Permission.GranteeType", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of Permission", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_apply_update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "path", "value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.permission_types.Permission._apply_update", "name": "_apply_update", "type": null}}, "_to_proto": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permission._to_proto", "name": "_to_proto", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.permission_types.Permission"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_to_proto of Permission", "ret_type": "google.ai.generativelanguage_v1beta.types.permission.Permission", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permission.delete", "name": "delete", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "client"], "arg_types": ["google.generativeai.types.permission_types.Permission", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.client.PermissionServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete of Permission", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "delete_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permission.delete_async", "name": "delete_async", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "client"], "arg_types": ["google.generativeai.types.permission_types.Permission", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.async_client.PermissionServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "delete_async of Permission", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "email_address": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.permission_types.Permission.email_address", "name": "email_address", "setter_type": null, "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "name", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permission.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "name", "client"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.permission_types.Permission"}, "builtins.str", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.client.PermissionServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get of Permission", "ret_type": "google.generativeai.types.permission_types.Permission", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.permission_types.Permission.get", "name": "get", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "name", "client"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.permission_types.Permission"}, "builtins.str", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.client.PermissionServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get of Permission", "ret_type": "google.generativeai.types.permission_types.Permission", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["cls", "name", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permission.get_async", "name": "get_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "name", "client"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.permission_types.Permission"}, "builtins.str", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.async_client.PermissionServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_async of Permission", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.generativeai.types.permission_types.Permission"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.permission_types.Permission.get_async", "name": "get_async", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["cls", "name", "client"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.permission_types.Permission"}, "builtins.str", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.async_client.PermissionServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_async of Permission", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.generativeai.types.permission_types.Permission"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "grantee_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.generativeai.types.permission_types.Permission.grantee_type", "name": "grantee_type", "setter_type": null, "type": {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.types.permission.Permission.GranteeType", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.generativeai.types.permission_types.Permission.name", "name": "name", "setter_type": null, "type": "builtins.str"}}, "role": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.generativeai.types.permission_types.Permission.role", "name": "role", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.types.permission.Permission.Role"}}, "to_dict": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permission.to_dict", "name": "to_dict", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.permission_types.Permission"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_dict of Permission", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "updates", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permission.update", "name": "update", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "updates", "client"], "arg_types": ["google.generativeai.types.permission_types.Permission", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.client.PermissionServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update of Permission", "ret_type": "google.generativeai.types.permission_types.Permission", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "update_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "updates", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permission.update_async", "name": "update_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "updates", "client"], "arg_types": ["google.generativeai.types.permission_types.Permission", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.async_client.PermissionServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "update_async of Permission", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.generativeai.types.permission_types.Permission"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.permission_types.Permission.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.permission_types.Permission", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Permissions": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.generativeai.types.permission_types.Permissions", "name": "Permissions", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.generativeai.types.permission_types.Permissions", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.generativeai.types.permission_types", "mro": ["google.generativeai.types.permission_types.Permissions", "builtins.object"], "names": {".class": "SymbolTable", "__aiter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "google.generativeai.types.permission_types.Permissions.__aiter__", "name": "__aiter__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "parent"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.permission_types.Permissions.__init__", "name": "__init__", "type": null}}, "__iter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.permission_types.Permissions.__iter__", "name": "__iter__", "type": null}}, "_make_create_permission_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "role", "grantee_type", "email_address"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permissions._make_create_permission_request", "name": "_make_create_permission_request", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "role", "grantee_type", "email_address"], "arg_types": ["google.generativeai.types.permission_types.Permissions", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.RoleOptions"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.GranteeTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_make_create_permission_request of Permissions", "ret_type": "google.ai.generativelanguage_v1beta.types.permission_service.CreatePermissionRequest", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_parent": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.generativeai.types.permission_types.Permissions._parent", "name": "_parent", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "create": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "role", "grantee_type", "email_address", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permissions.create", "name": "create", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "role", "grantee_type", "email_address", "client"], "arg_types": ["google.generativeai.types.permission_types.Permissions", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.RoleOptions"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.GranteeTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.client.PermissionServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create of Permissions", "ret_type": "google.generativeai.types.permission_types.Permission", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "create_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "role", "grantee_type", "email_address", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permissions.create_async", "name": "create_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "role", "grantee_type", "email_address", "client"], "arg_types": ["google.generativeai.types.permission_types.Permissions", {".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.RoleOptions"}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.GranteeTypeOptions"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.async_client.PermissionServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "create_async of Permissions", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.generativeai.types.permission_types.Permission"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permissions.get", "name": "get", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.permission_types.Permissions"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get of Permissions", "ret_type": "google.generativeai.types.permission_types.Permission", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.permission_types.Permissions.get", "name": "get", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.permission_types.Permissions"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get of Permissions", "ret_type": "google.generativeai.types.permission_types.Permission", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_coroutine", "is_decorated", "is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permissions.get_async", "name": "get_async", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.permission_types.Permissions"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_async of Permissions", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.generativeai.types.permission_types.Permission"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.permission_types.Permissions.get_async", "name": "get_async", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "name"], "arg_types": [{".class": "TypeType", "item": "google.generativeai.types.permission_types.Permissions"}, "builtins.str"], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_async of Permissions", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "google.generativeai.types.permission_types.Permission"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "page_size", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permissions.list", "name": "list", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "page_size", "client"], "arg_types": ["google.generativeai.types.permission_types.Permissions", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.client.PermissionServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list of Permissions", "ret_type": {".class": "Instance", "args": ["google.generativeai.types.permission_types.Permission"], "extra_attrs": null, "type_ref": "typing.Iterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "list_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 1], "arg_names": ["self", "page_size", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_generator", "is_coroutine", "is_async_generator", "is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permissions.list_async", "name": "list_async", "type": {".class": "CallableType", "arg_kinds": [0, 1, 1], "arg_names": ["self", "page_size", "client"], "arg_types": ["google.generativeai.types.permission_types.Permissions", {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.async_client.PermissionServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_async of Permissions", "ret_type": {".class": "Instance", "args": ["google.generativeai.types.permission_types.Permission"], "extra_attrs": null, "type_ref": "typing.AsyncIterable"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parent": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.generativeai.types.permission_types.Permissions.parent", "name": "parent", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.permission_types.Permissions.parent", "name": "parent", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.generativeai.types.permission_types.Permissions"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parent of Permissions", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "transfer_ownership": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "email_address", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permissions.transfer_ownership", "name": "transfer_ownership", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "email_address", "client"], "arg_types": ["google.generativeai.types.permission_types.Permissions", "builtins.str", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.client.PermissionServiceClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transfer_ownership of Permissions", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "transfer_ownership_async": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "email_address", "client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_trivial_self"], "fullname": "google.generativeai.types.permission_types.Permissions.transfer_ownership_async", "name": "transfer_ownership_async", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "email_address", "client"], "arg_types": ["google.generativeai.types.permission_types.Permissions", "builtins.str", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.permission_service.async_client.PermissionServiceAsyncClient", {".class": "NoneType"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "transfer_ownership_async of Permissions", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "NoneType"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.generativeai.types.permission_types.Permissions.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.generativeai.types.permission_types.Permissions", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Role": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.permission_types.Role", "line": 34, "no_args": true, "normalized": false, "python_3_12_type_alias": false, "target": "google.ai.generativelanguage_v1beta.types.permission.Permission.Role"}}, "RoleOptions": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "google.generativeai.types.permission_types.RoleOptions", "line": 37, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["builtins.str", "builtins.int", "google.ai.generativelanguage_v1beta.types.permission.Permission.Role"], "uses_pep604_syntax": false}}}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_GRANTEE_TYPE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.permission_types._GRANTEE_TYPE", "name": "_GRANTEE_TYPE", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.GranteeTypeOptions"}, "google.ai.generativelanguage_v1beta.types.permission.Permission.GranteeType"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_ROLE": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "has_explicit_value"], "fullname": "google.generativeai.types.permission_types._ROLE", "name": "_ROLE", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.RoleOptions"}, "google.ai.generativelanguage_v1beta.types.permission.Permission.Role"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "_VALID_PERMISSION_ID": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.permission_types._VALID_PERMISSION_ID", "name": "_VALID_PERMISSION_ID", "setter_type": null, "type": "builtins.str"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.generativeai.types.permission_types.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.permission_types.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.permission_types.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.permission_types.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.permission_types.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.permission_types.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.generativeai.types.permission_types.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef", "module_public": false}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_public": false}, "field_mask_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.generativeai.types.permission_types.field_mask_pb2", "name": "field_mask_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.generativeai.types.permission_types.field_mask_pb2", "source_any": null, "type_of_any": 3}}}, "flatten_update_paths": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.utils.flatten_update_paths", "kind": "Gdef", "module_public": false}, "get_default_permission_async_client": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.client.get_default_permission_async_client", "kind": "Gdef", "module_public": false}, "get_default_permission_client": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.client.get_default_permission_client", "kind": "Gdef", "module_public": false}, "glm": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage", "kind": "Gdef", "module_public": false}, "protos": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.protos", "kind": "Gdef", "module_public": false}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef", "module_public": false}, "string_utils": {".class": "SymbolTableNode", "cross_ref": "google.generativeai.string_utils", "kind": "Gdef", "module_public": false}, "to_grantee_type": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.permission_types.to_grantee_type", "name": "to_grantee_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.GranteeTypeOptions"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_grantee_type", "ret_type": "google.ai.generativelanguage_v1beta.types.permission.Permission.GranteeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "to_role": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["x"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.permission_types.to_role", "name": "to_role", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["x"], "arg_types": [{".class": "TypeAliasType", "args": [], "type_ref": "google.generativeai.types.permission_types.RoleOptions"}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "to_role", "ret_type": "google.ai.generativelanguage_v1beta.types.permission.Permission.Role", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "valid_id": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.generativeai.types.permission_types.valid_id", "name": "valid_id", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["name"], "arg_types": ["builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "valid_id", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\types\\permission_types.py"}