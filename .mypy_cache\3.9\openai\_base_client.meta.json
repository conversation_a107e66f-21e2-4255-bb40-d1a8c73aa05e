{"data_mtime": 1754400650, "dep_lines": [12, 41, 42, 43, 44, 61, 62, 63, 64, 70, 79, 86, 103, 1, 3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 14, 15, 32, 34, 35, 36, 37, 41, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1317], "dep_prios": [10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 10, 10, 10, 5, 5, 5, 5, 10, 5, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 10], "dependencies": ["email.utils", "openai._exceptions", "openai._qs", "openai._files", "openai._types", "openai._utils", "openai._compat", "openai._models", "openai._response", "openai._constants", "openai._streaming", "openai._legacy_response", "httpx._config", "__future__", "sys", "json", "time", "uuid", "email", "asyncio", "inspect", "logging", "platform", "types", "random", "typing", "typing_extensions", "anyio", "httpx", "distro", "pydantic", "openai", "builtins", "_frozen_importlib", "_ssl", "_typeshed", "abc", "http", "http.cookiejar", "httpx._auth", "httpx._client", "httpx._exceptions", "httpx._models", "httpx._transports", "httpx._transports.base", "httpx._urls", "openai._utils._logs", "openai._utils._sync", "openai._utils._utils", "os", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.fields", "pydantic.main", "ssl"], "hash": "689b73ddd2bf59f977ef2e45346a9ea422d41fc8", "id": "openai._base_client", "ignore_all": true, "interface_hash": "aad31f777dae1d1fe1df7f264b4265fb29a280c2", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_base_client.py", "plugin_data": null, "size": 68112, "suppressed": ["httpx_aiohttp"], "version_id": "1.17.1"}