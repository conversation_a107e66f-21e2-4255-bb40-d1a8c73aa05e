{"data_mtime": 1754400649, "dep_lines": [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 6, 7, 3, 4, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.beta.realtime.session_update_event", "openai.types.beta.realtime.response_cancel_event", "openai.types.beta.realtime.response_create_event", "openai.types.beta.realtime.transcription_session_update", "openai.types.beta.realtime.conversation_item_create_event", "openai.types.beta.realtime.conversation_item_delete_event", "openai.types.beta.realtime.input_audio_buffer_clear_event", "openai.types.beta.realtime.input_audio_buffer_append_event", "openai.types.beta.realtime.input_audio_buffer_commit_event", "openai.types.beta.realtime.conversation_item_retrieve_event", "openai.types.beta.realtime.conversation_item_truncate_event", "openai._utils", "openai._models", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "3c5ed3ee342665229ba0706e3b811026adb5222b", "id": "openai.types.beta.realtime.realtime_client_event", "ignore_all": true, "interface_hash": "ee771a146a4ce739e021a32862f2fc9f7b38ee0b", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\beta\\realtime\\realtime_client_event.py", "plugin_data": null, "size": 1839, "suppressed": [], "version_id": "1.17.1"}