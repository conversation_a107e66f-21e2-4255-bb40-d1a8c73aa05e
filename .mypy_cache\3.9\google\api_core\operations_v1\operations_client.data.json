{".class": "MypyFile", "_fullname": "google.api_core.operations_v1.operations_client", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Compression": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.operations_client.Compression", "name": "Compression", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.api_core.operations_v1.operations_client.Compression", "source_any": null, "type_of_any": 3}}}, "OperationsClient": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.api_core.operations_v1.operations_client.OperationsClient", "name": "OperationsClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.api_core.operations_v1.operations_client.OperationsClient", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.api_core.operations_v1.operations_client", "mro": ["google.api_core.operations_v1.operations_client.OperationsClient", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "channel", "client_config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.operations_v1.operations_client.OperationsClient.__init__", "name": "__init__", "type": null}}, "_cancel_operation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.operations_v1.operations_client.OperationsClient._cancel_operation", "name": "_cancel_operation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_delete_operation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.operations_v1.operations_client.OperationsClient._delete_operation", "name": "_delete_operation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_get_operation": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.operations_v1.operations_client.OperationsClient._get_operation", "name": "_get_operation", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "_list_operations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.operations_v1.operations_client.OperationsClient._list_operations", "name": "_list_operations", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "cancel_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "retry", "timeout", "compression", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.operations_v1.operations_client.OperationsClient.cancel_operation", "name": "cancel_operation", "type": null}}, "delete_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "retry", "timeout", "compression", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.operations_v1.operations_client.OperationsClient.delete_operation", "name": "delete_operation", "type": null}}, "get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "retry", "timeout", "compression", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.operations_v1.operations_client.OperationsClient.get_operation", "name": "get_operation", "type": null}}, "list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1, 1, 1, 1], "arg_names": ["self", "name", "filter_", "retry", "timeout", "compression", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.api_core.operations_v1.operations_client.OperationsClient.list_operations", "name": "list_operations", "type": null}}, "operations_stub": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.api_core.operations_v1.operations_client.OperationsClient.operations_stub", "name": "operations_stub", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.api_core.operations_v1.operations_client.OperationsClient.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.api_core.operations_v1.operations_client.OperationsClient", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.operations_client.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.operations_client.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.operations_client.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.operations_client.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.operations_client.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.api_core.operations_v1.operations_client.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "core_exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef"}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.api_core.operations_v1.operations_client.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.api_core.operations_v1.operations_client.operations_pb2", "source_any": null, "type_of_any": 3}}}, "page_iterator": {".class": "SymbolTableNode", "cross_ref": "google.api_core.page_iterator", "kind": "Gdef"}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry", "kind": "Gdef"}, "timeouts": {".class": "SymbolTableNode", "cross_ref": "google.api_core.timeout", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\api_core\\operations_v1\\operations_client.py"}