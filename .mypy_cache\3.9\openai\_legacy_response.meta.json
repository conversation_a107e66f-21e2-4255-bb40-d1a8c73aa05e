{"data_mtime": 1754400649, "dep_lines": [26, 27, 28, 29, 30, 31, 35, 1, 3, 4, 5, 6, 7, 8, 20, 22, 23, 24, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 5, 5, 10, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai._types", "openai._utils", "openai._models", "openai._constants", "openai._streaming", "openai._exceptions", "openai._base_client", "__future__", "os", "inspect", "logging", "datetime", "functools", "typing", "typing_extensions", "anyio", "httpx", "pydantic", "builtins", "_frozen_importlib", "abc", "httpx._models", "httpx._urls", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "2d34c0da23e6b140f8b218092beefcf94ae5fef6", "id": "openai._legacy_response", "ignore_all": true, "interface_hash": "e007647331f3a4680034ed4c8d993178c14cd5c6", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\_legacy_response.py", "plugin_data": null, "size": 16237, "suppressed": [], "version_id": "1.17.1"}