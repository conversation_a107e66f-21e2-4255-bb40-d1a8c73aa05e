{"data_mtime": 1754400649, "dep_lines": [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 3, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["openai.types.audio.translation", "openai.types.audio.speech_model", "openai.types.audio.transcription", "openai.types.audio.transcription_word", "openai.types.audio.translation_verbose", "openai.types.audio.speech_create_params", "openai.types.audio.transcription_include", "openai.types.audio.transcription_segment", "openai.types.audio.transcription_verbose", "openai.types.audio.translation_create_params", "openai.types.audio.transcription_stream_event", "openai.types.audio.transcription_create_params", "openai.types.audio.translation_create_response", "openai.types.audio.transcription_create_response", "openai.types.audio.transcription_text_done_event", "openai.types.audio.transcription_text_delta_event", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "a9856fbba8e2728bfc836f0219f823d6acb129df", "id": "openai.types.audio", "ignore_all": true, "interface_hash": "79bcf2d4db1a44d48f1ca9d318ec95baf9ee4135", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\audio\\__init__.py", "plugin_data": null, "size": 1426, "suppressed": [], "version_id": "1.17.1"}