{"data_mtime": 1754400654, "dep_lines": [21, 22, 23, 24, 28, 29, 33, 37, 41, 42, 50, 51, 52, 73, 82, 83, 91, 120, 121, 133, 134, 144, 145, 155, 185, 192, 205, 16, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 30, 30, 30], "dependencies": ["google.ai.generativelanguage_v1beta.services.cache_service", "google.ai.generativelanguage_v1beta.services.discuss_service", "google.ai.generativelanguage_v1beta.services.file_service", "google.ai.generativelanguage_v1beta.services.generative_service", "google.ai.generativelanguage_v1beta.services.model_service", "google.ai.generativelanguage_v1beta.services.permission_service", "google.ai.generativelanguage_v1beta.services.prediction_service", "google.ai.generativelanguage_v1beta.services.retriever_service", "google.ai.generativelanguage_v1beta.services.text_service", "google.ai.generativelanguage_v1beta.types.cache_service", "google.ai.generativelanguage_v1beta.types.cached_content", "google.ai.generativelanguage_v1beta.types.citation", "google.ai.generativelanguage_v1beta.types.content", "google.ai.generativelanguage_v1beta.types.discuss_service", "google.ai.generativelanguage_v1beta.types.file", "google.ai.generativelanguage_v1beta.types.file_service", "google.ai.generativelanguage_v1beta.types.generative_service", "google.ai.generativelanguage_v1beta.types.model", "google.ai.generativelanguage_v1beta.types.model_service", "google.ai.generativelanguage_v1beta.types.permission", "google.ai.generativelanguage_v1beta.types.permission_service", "google.ai.generativelanguage_v1beta.types.prediction_service", "google.ai.generativelanguage_v1beta.types.retriever", "google.ai.generativelanguage_v1beta.types.retriever_service", "google.ai.generativelanguage_v1beta.types.safety", "google.ai.generativelanguage_v1beta.types.text_service", "google.ai.generativelanguage_v1beta.types.tuned_model", "google.ai.generativelanguage_v1beta.gapic_version", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "bb57f922006b2206eca160db5c1cb4165795de4b", "id": "google.ai.generativelanguage_v1beta", "ignore_all": true, "interface_hash": "7fc862f92e5771305d2b3b31e91811fa3e60e2ef", "mtime": 1754241453, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\__init__.py", "plugin_data": null, "size": 9935, "suppressed": [], "version_id": "1.17.1"}