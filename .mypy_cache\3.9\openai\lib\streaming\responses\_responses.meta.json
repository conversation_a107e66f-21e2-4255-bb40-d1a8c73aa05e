{"data_mtime": 1754400650, "dep_lines": [8, 9, 21, 22, 23, 20, 16, 17, 18, 19, 1, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 5, 5, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.lib.streaming.responses._types", "openai.lib.streaming.responses._events", "openai.lib._parsing._responses", "openai.types.responses.tool_param", "openai.types.responses.parsed_response", "openai.types.responses", "openai._types", "openai._utils", "openai._models", "openai._streaming", "__future__", "inspect", "types", "typing", "typing_extensions", "builtins", "_frozen_importlib", "abc", "httpx", "httpx._models", "openai._compat", "openai._utils._utils", "openai.lib._parsing", "openai.types", "openai.types.responses.computer_tool_param", "openai.types.responses.file_search_tool_param", "openai.types.responses.function_tool_param", "openai.types.responses.response", "openai.types.responses.response_audio_delta_event", "openai.types.responses.response_audio_done_event", "openai.types.responses.response_audio_transcript_delta_event", "openai.types.responses.response_audio_transcript_done_event", "openai.types.responses.response_code_interpreter_call_code_delta_event", "openai.types.responses.response_code_interpreter_call_code_done_event", "openai.types.responses.response_code_interpreter_call_completed_event", "openai.types.responses.response_code_interpreter_call_in_progress_event", "openai.types.responses.response_code_interpreter_call_interpreting_event", "openai.types.responses.response_code_interpreter_tool_call", "openai.types.responses.response_completed_event", "openai.types.responses.response_computer_tool_call", "openai.types.responses.response_content_part_added_event", "openai.types.responses.response_content_part_done_event", "openai.types.responses.response_created_event", "openai.types.responses.response_error_event", "openai.types.responses.response_failed_event", "openai.types.responses.response_file_search_call_completed_event", "openai.types.responses.response_file_search_call_in_progress_event", "openai.types.responses.response_file_search_call_searching_event", "openai.types.responses.response_file_search_tool_call", "openai.types.responses.response_function_call_arguments_delta_event", "openai.types.responses.response_function_call_arguments_done_event", "openai.types.responses.response_function_tool_call", "openai.types.responses.response_function_web_search", "openai.types.responses.response_image_gen_call_completed_event", "openai.types.responses.response_image_gen_call_generating_event", "openai.types.responses.response_image_gen_call_in_progress_event", "openai.types.responses.response_image_gen_call_partial_image_event", "openai.types.responses.response_in_progress_event", "openai.types.responses.response_incomplete_event", "openai.types.responses.response_mcp_call_arguments_delta_event", "openai.types.responses.response_mcp_call_arguments_done_event", "openai.types.responses.response_mcp_call_completed_event", "openai.types.responses.response_mcp_call_failed_event", "openai.types.responses.response_mcp_call_in_progress_event", "openai.types.responses.response_mcp_list_tools_completed_event", "openai.types.responses.response_mcp_list_tools_failed_event", "openai.types.responses.response_mcp_list_tools_in_progress_event", "openai.types.responses.response_output_item", "openai.types.responses.response_output_item_added_event", "openai.types.responses.response_output_item_done_event", "openai.types.responses.response_output_message", "openai.types.responses.response_output_refusal", "openai.types.responses.response_output_text", "openai.types.responses.response_output_text_annotation_added_event", "openai.types.responses.response_queued_event", "openai.types.responses.response_reasoning_item", "openai.types.responses.response_reasoning_summary_delta_event", "openai.types.responses.response_reasoning_summary_done_event", "openai.types.responses.response_reasoning_summary_part_added_event", "openai.types.responses.response_reasoning_summary_part_done_event", "openai.types.responses.response_reasoning_summary_text_delta_event", "openai.types.responses.response_reasoning_summary_text_done_event", "openai.types.responses.response_refusal_delta_event", "openai.types.responses.response_refusal_done_event", "openai.types.responses.response_text_delta_event", "openai.types.responses.response_text_done_event", "openai.types.responses.response_web_search_call_completed_event", "openai.types.responses.response_web_search_call_in_progress_event", "openai.types.responses.response_web_search_call_searching_event", "openai.types.responses.web_search_tool_param", "openai.types.shared_params", "openai.types.shared_params.comparison_filter", "openai.types.shared_params.compound_filter", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "673907b38e729c73c8335ab898f92b7b4a4439e0", "id": "openai.lib.streaming.responses._responses", "ignore_all": true, "interface_hash": "29858fbd78c9b3cf4446336aaa08602568c23832", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\streaming\\responses\\_responses.py", "plugin_data": null, "size": 13672, "suppressed": [], "version_id": "1.17.1"}