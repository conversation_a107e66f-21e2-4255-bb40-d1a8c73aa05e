{".class": "MypyFile", "_fullname": "openai.resources.fine_tuning.alpha", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Alpha": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.Alpha", "kind": "Gdef"}, "AlphaWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.AlphaWithRawResponse", "kind": "Gdef"}, "AlphaWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.AlphaWithStreamingResponse", "kind": "Gdef"}, "AsyncAlpha": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.AsyncAlpha", "kind": "Gdef"}, "AsyncAlphaWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithRawResponse", "kind": "Gdef"}, "AsyncAlphaWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.alpha.AsyncAlphaWithStreamingResponse", "kind": "Gdef"}, "AsyncGraders": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.AsyncGraders", "kind": "Gdef"}, "AsyncGradersWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.AsyncGradersWithRawResponse", "kind": "Gdef"}, "AsyncGradersWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.AsyncGradersWithStreamingResponse", "kind": "Gdef"}, "Graders": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.Graders", "kind": "Gdef"}, "GradersWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.GradersWithRawResponse", "kind": "Gdef"}, "GradersWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.fine_tuning.alpha.graders.GradersWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.fine_tuning.alpha.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.fine_tuning.alpha.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\fine_tuning\\alpha\\__init__.py"}