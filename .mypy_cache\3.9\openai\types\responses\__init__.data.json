{".class": "MypyFile", "_fullname": "openai.types.responses", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ComputerTool": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.computer_tool.ComputerTool", "kind": "Gdef"}, "ComputerToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.computer_tool_param.ComputerToolParam", "kind": "Gdef"}, "EasyInputMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.easy_input_message.EasyInputMessage", "kind": "Gdef"}, "EasyInputMessageParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.easy_input_message_param.EasyInputMessageParam", "kind": "Gdef"}, "FileSearchTool": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.file_search_tool.FileSearchTool", "kind": "Gdef"}, "FileSearchToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.file_search_tool_param.FileSearchToolParam", "kind": "Gdef"}, "FunctionTool": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.function_tool.FunctionTool", "kind": "Gdef"}, "FunctionToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.function_tool_param.FunctionToolParam", "kind": "Gdef"}, "InputItemListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.input_item_list_params.InputItemListParams", "kind": "Gdef"}, "ParsedContent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedContent", "kind": "Gdef"}, "ParsedResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponse", "kind": "Gdef"}, "ParsedResponseFunctionToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall", "kind": "Gdef"}, "ParsedResponseOutputItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponseOutputItem", "kind": "Gdef"}, "ParsedResponseOutputMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "kind": "Gdef"}, "ParsedResponseOutputText": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponseOutputText", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response.Response", "kind": "Gdef"}, "ResponseAudioDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_delta_event.ResponseAudioDeltaEvent", "kind": "Gdef"}, "ResponseAudioDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_done_event.ResponseAudioDoneEvent", "kind": "Gdef"}, "ResponseAudioTranscriptDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_transcript_delta_event.ResponseAudioTranscriptDeltaEvent", "kind": "Gdef"}, "ResponseAudioTranscriptDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_transcript_done_event.ResponseAudioTranscriptDoneEvent", "kind": "Gdef"}, "ResponseCodeInterpreterCallCodeDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_code_delta_event.ResponseCodeInterpreterCallCodeDeltaEvent", "kind": "Gdef"}, "ResponseCodeInterpreterCallCodeDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_code_done_event.ResponseCodeInterpreterCallCodeDoneEvent", "kind": "Gdef"}, "ResponseCodeInterpreterCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_completed_event.ResponseCodeInterpreterCallCompletedEvent", "kind": "Gdef"}, "ResponseCodeInterpreterCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_in_progress_event.ResponseCodeInterpreterCallInProgressEvent", "kind": "Gdef"}, "ResponseCodeInterpreterCallInterpretingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_interpreting_event.ResponseCodeInterpreterCallInterpretingEvent", "kind": "Gdef"}, "ResponseCodeInterpreterToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_tool_call.ResponseCodeInterpreterToolCall", "kind": "Gdef"}, "ResponseCodeInterpreterToolCallParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_tool_call_param.ResponseCodeInterpreterToolCallParam", "kind": "Gdef"}, "ResponseCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_completed_event.ResponseCompletedEvent", "kind": "Gdef"}, "ResponseComputerToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_computer_tool_call.ResponseComputerToolCall", "kind": "Gdef"}, "ResponseComputerToolCallOutputItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_computer_tool_call_output_item.ResponseComputerToolCallOutputItem", "kind": "Gdef"}, "ResponseComputerToolCallOutputScreenshot": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_computer_tool_call_output_screenshot.ResponseComputerToolCallOutputScreenshot", "kind": "Gdef"}, "ResponseComputerToolCallOutputScreenshotParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_computer_tool_call_output_screenshot_param.ResponseComputerToolCallOutputScreenshotParam", "kind": "Gdef"}, "ResponseComputerToolCallParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_computer_tool_call_param.ResponseComputerToolCallParam", "kind": "Gdef"}, "ResponseContentPartAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_content_part_added_event.ResponseContentPartAddedEvent", "kind": "Gdef"}, "ResponseContentPartDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_content_part_done_event.ResponseContentPartDoneEvent", "kind": "Gdef"}, "ResponseCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_create_params.ResponseCreateParams", "kind": "Gdef"}, "ResponseCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_created_event.ResponseCreatedEvent", "kind": "Gdef"}, "ResponseError": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_error.ResponseError", "kind": "Gdef"}, "ResponseErrorEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_error_event.ResponseErrorEvent", "kind": "Gdef"}, "ResponseFailedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_failed_event.ResponseFailedEvent", "kind": "Gdef"}, "ResponseFileSearchCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_call_completed_event.ResponseFileSearchCallCompletedEvent", "kind": "Gdef"}, "ResponseFileSearchCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_call_in_progress_event.ResponseFileSearchCallInProgressEvent", "kind": "Gdef"}, "ResponseFileSearchCallSearchingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_call_searching_event.ResponseFileSearchCallSearchingEvent", "kind": "Gdef"}, "ResponseFileSearchToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_tool_call.ResponseFileSearchToolCall", "kind": "Gdef"}, "ResponseFileSearchToolCallParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_tool_call_param.ResponseFileSearchToolCallParam", "kind": "Gdef"}, "ResponseFormatTextConfig": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_format_text_config.ResponseFormatTextConfig", "kind": "Gdef"}, "ResponseFormatTextConfigParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_format_text_config_param.ResponseFormatTextConfigParam", "kind": "Gdef"}, "ResponseFormatTextJSONSchemaConfig": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_format_text_json_schema_config.ResponseFormatTextJSONSchemaConfig", "kind": "Gdef"}, "ResponseFormatTextJSONSchemaConfigParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_format_text_json_schema_config_param.ResponseFormatTextJSONSchemaConfigParam", "kind": "Gdef"}, "ResponseFunctionCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_call_arguments_delta_event.ResponseFunctionCallArgumentsDeltaEvent", "kind": "Gdef"}, "ResponseFunctionCallArgumentsDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_call_arguments_done_event.ResponseFunctionCallArgumentsDoneEvent", "kind": "Gdef"}, "ResponseFunctionToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_tool_call.ResponseFunctionToolCall", "kind": "Gdef"}, "ResponseFunctionToolCallItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_tool_call_item.ResponseFunctionToolCallItem", "kind": "Gdef"}, "ResponseFunctionToolCallOutputItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_tool_call_output_item.ResponseFunctionToolCallOutputItem", "kind": "Gdef"}, "ResponseFunctionToolCallParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_tool_call_param.ResponseFunctionToolCallParam", "kind": "Gdef"}, "ResponseFunctionWebSearch": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_web_search.ResponseFunctionWebSearch", "kind": "Gdef"}, "ResponseFunctionWebSearchParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_web_search_param.ResponseFunctionWebSearchParam", "kind": "Gdef"}, "ResponseImageGenCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_completed_event.ResponseImageGenCallCompletedEvent", "kind": "Gdef"}, "ResponseImageGenCallGeneratingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_generating_event.ResponseImageGenCallGeneratingEvent", "kind": "Gdef"}, "ResponseImageGenCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_in_progress_event.ResponseImageGenCallInProgressEvent", "kind": "Gdef"}, "ResponseImageGenCallPartialImageEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_partial_image_event.ResponseImageGenCallPartialImageEvent", "kind": "Gdef"}, "ResponseInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_in_progress_event.ResponseInProgressEvent", "kind": "Gdef"}, "ResponseIncludable": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_includable.ResponseIncludable", "kind": "Gdef"}, "ResponseIncompleteEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_incomplete_event.ResponseIncompleteEvent", "kind": "Gdef"}, "ResponseInputContent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_content.ResponseInputContent", "kind": "Gdef"}, "ResponseInputContentParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_content_param.ResponseInputContentParam", "kind": "Gdef"}, "ResponseInputFile": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_file.ResponseInputFile", "kind": "Gdef"}, "ResponseInputFileParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_file_param.ResponseInputFileParam", "kind": "Gdef"}, "ResponseInputImage": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_image.ResponseInputImage", "kind": "Gdef"}, "ResponseInputImageParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_image_param.ResponseInputImageParam", "kind": "Gdef"}, "ResponseInputItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_item.ResponseInputItem", "kind": "Gdef"}, "ResponseInputItemParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_item_param.ResponseInputItemParam", "kind": "Gdef"}, "ResponseInputMessageContentList": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_message_content_list.ResponseInputMessageContentList", "kind": "Gdef"}, "ResponseInputMessageContentListParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_message_content_list_param.ResponseInputMessageContentListParam", "kind": "Gdef"}, "ResponseInputMessageItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_message_item.ResponseInputMessageItem", "kind": "Gdef"}, "ResponseInputParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_param.ResponseInputParam", "kind": "Gdef"}, "ResponseInputText": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_text.ResponseInputText", "kind": "Gdef"}, "ResponseInputTextParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_input_text_param.ResponseInputTextParam", "kind": "Gdef"}, "ResponseItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_item.ResponseItem", "kind": "Gdef"}, "ResponseItemList": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_item_list.ResponseItemList", "kind": "Gdef"}, "ResponseMcpCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_arguments_delta_event.ResponseMcpCallArgumentsDeltaEvent", "kind": "Gdef"}, "ResponseMcpCallArgumentsDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_arguments_done_event.ResponseMcpCallArgumentsDoneEvent", "kind": "Gdef"}, "ResponseMcpCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_completed_event.ResponseMcpCallCompletedEvent", "kind": "Gdef"}, "ResponseMcpCallFailedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_failed_event.ResponseMcpCallFailedEvent", "kind": "Gdef"}, "ResponseMcpCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_in_progress_event.ResponseMcpCallInProgressEvent", "kind": "Gdef"}, "ResponseMcpListToolsCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_list_tools_completed_event.ResponseMcpListToolsCompletedEvent", "kind": "Gdef"}, "ResponseMcpListToolsFailedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_list_tools_failed_event.ResponseMcpListToolsFailedEvent", "kind": "Gdef"}, "ResponseMcpListToolsInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_list_tools_in_progress_event.ResponseMcpListToolsInProgressEvent", "kind": "Gdef"}, "ResponseOutputItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item.ResponseOutputItem", "kind": "Gdef"}, "ResponseOutputItemAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item_added_event.ResponseOutputItemAddedEvent", "kind": "Gdef"}, "ResponseOutputItemDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item_done_event.ResponseOutputItemDoneEvent", "kind": "Gdef"}, "ResponseOutputMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_message.ResponseOutputMessage", "kind": "Gdef"}, "ResponseOutputMessageParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_message_param.ResponseOutputMessageParam", "kind": "Gdef"}, "ResponseOutputRefusal": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_refusal.ResponseOutputRefusal", "kind": "Gdef"}, "ResponseOutputRefusalParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_refusal_param.ResponseOutputRefusalParam", "kind": "Gdef"}, "ResponseOutputText": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_text.ResponseOutputText", "kind": "Gdef"}, "ResponseOutputTextAnnotationAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_text_annotation_added_event.ResponseOutputTextAnnotationAddedEvent", "kind": "Gdef"}, "ResponseOutputTextParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_text_param.ResponseOutputTextParam", "kind": "Gdef"}, "ResponsePrompt": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_prompt.ResponsePrompt", "kind": "Gdef"}, "ResponsePromptParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_prompt_param.ResponsePromptParam", "kind": "Gdef"}, "ResponseQueuedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_queued_event.ResponseQueuedEvent", "kind": "Gdef"}, "ResponseReasoningItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_item.ResponseReasoningItem", "kind": "Gdef"}, "ResponseReasoningItemParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_item_param.ResponseReasoningItemParam", "kind": "Gdef"}, "ResponseReasoningSummaryDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_delta_event.ResponseReasoningSummaryDeltaEvent", "kind": "Gdef"}, "ResponseReasoningSummaryDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_done_event.ResponseReasoningSummaryDoneEvent", "kind": "Gdef"}, "ResponseReasoningSummaryPartAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_part_added_event.ResponseReasoningSummaryPartAddedEvent", "kind": "Gdef"}, "ResponseReasoningSummaryPartDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_part_done_event.ResponseReasoningSummaryPartDoneEvent", "kind": "Gdef"}, "ResponseReasoningSummaryTextDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_text_delta_event.ResponseReasoningSummaryTextDeltaEvent", "kind": "Gdef"}, "ResponseReasoningSummaryTextDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_text_done_event.ResponseReasoningSummaryTextDoneEvent", "kind": "Gdef"}, "ResponseRefusalDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_refusal_delta_event.ResponseRefusalDeltaEvent", "kind": "Gdef"}, "ResponseRefusalDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_refusal_done_event.ResponseRefusalDoneEvent", "kind": "Gdef"}, "ResponseRetrieveParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_retrieve_params.ResponseRetrieveParams", "kind": "Gdef"}, "ResponseStatus": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_status.ResponseStatus", "kind": "Gdef"}, "ResponseStreamEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_stream_event.ResponseStreamEvent", "kind": "Gdef"}, "ResponseTextConfig": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_text_config.ResponseTextConfig", "kind": "Gdef"}, "ResponseTextConfigParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_text_config_param.ResponseTextConfigParam", "kind": "Gdef"}, "ResponseTextDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_text_delta_event.ResponseTextDeltaEvent", "kind": "Gdef"}, "ResponseTextDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_text_done_event.ResponseTextDoneEvent", "kind": "Gdef"}, "ResponseUsage": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_usage.ResponseUsage", "kind": "Gdef"}, "ResponseWebSearchCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_web_search_call_completed_event.ResponseWebSearchCallCompletedEvent", "kind": "Gdef"}, "ResponseWebSearchCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_web_search_call_in_progress_event.ResponseWebSearchCallInProgressEvent", "kind": "Gdef"}, "ResponseWebSearchCallSearchingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_web_search_call_searching_event.ResponseWebSearchCallSearchingEvent", "kind": "Gdef"}, "Tool": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.tool.Tool", "kind": "Gdef"}, "ToolChoiceFunction": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.tool_choice_function.ToolChoiceFunction", "kind": "Gdef"}, "ToolChoiceFunctionParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.tool_choice_function_param.ToolChoiceFunctionParam", "kind": "Gdef"}, "ToolChoiceMcp": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.tool_choice_mcp.ToolChoiceMcp", "kind": "Gdef"}, "ToolChoiceMcpParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.tool_choice_mcp_param.ToolChoiceMcpParam", "kind": "Gdef"}, "ToolChoiceOptions": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.tool_choice_options.ToolChoiceOptions", "kind": "Gdef"}, "ToolChoiceTypes": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.tool_choice_types.ToolChoiceTypes", "kind": "Gdef"}, "ToolChoiceTypesParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.tool_choice_types_param.ToolChoiceTypesParam", "kind": "Gdef"}, "ToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.tool_param.ToolParam", "kind": "Gdef"}, "WebSearchTool": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.web_search_tool.WebSearchTool", "kind": "Gdef"}, "WebSearchToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.web_search_tool_param.WebSearchToolParam", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\__init__.py"}