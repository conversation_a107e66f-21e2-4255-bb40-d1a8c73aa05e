{".class": "MypyFile", "_fullname": "openai.types.responses.response_stream_event", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef", "module_public": false}, "PropertyInfo": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.PropertyInfo", "kind": "Gdef", "module_public": false}, "ResponseAudioDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_delta_event.ResponseAudioDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseAudioDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_done_event.ResponseAudioDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseAudioTranscriptDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_transcript_delta_event.ResponseAudioTranscriptDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseAudioTranscriptDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_audio_transcript_done_event.ResponseAudioTranscriptDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseCodeInterpreterCallCodeDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_code_delta_event.ResponseCodeInterpreterCallCodeDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseCodeInterpreterCallCodeDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_code_done_event.ResponseCodeInterpreterCallCodeDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseCodeInterpreterCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_completed_event.ResponseCodeInterpreterCallCompletedEvent", "kind": "Gdef", "module_public": false}, "ResponseCodeInterpreterCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_in_progress_event.ResponseCodeInterpreterCallInProgressEvent", "kind": "Gdef", "module_public": false}, "ResponseCodeInterpreterCallInterpretingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_call_interpreting_event.ResponseCodeInterpreterCallInterpretingEvent", "kind": "Gdef", "module_public": false}, "ResponseCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_completed_event.ResponseCompletedEvent", "kind": "Gdef", "module_public": false}, "ResponseContentPartAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_content_part_added_event.ResponseContentPartAddedEvent", "kind": "Gdef", "module_public": false}, "ResponseContentPartDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_content_part_done_event.ResponseContentPartDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseCreatedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_created_event.ResponseCreatedEvent", "kind": "Gdef", "module_public": false}, "ResponseErrorEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_error_event.ResponseErrorEvent", "kind": "Gdef", "module_public": false}, "ResponseFailedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_failed_event.ResponseFailedEvent", "kind": "Gdef", "module_public": false}, "ResponseFileSearchCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_call_completed_event.ResponseFileSearchCallCompletedEvent", "kind": "Gdef", "module_public": false}, "ResponseFileSearchCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_call_in_progress_event.ResponseFileSearchCallInProgressEvent", "kind": "Gdef", "module_public": false}, "ResponseFileSearchCallSearchingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_call_searching_event.ResponseFileSearchCallSearchingEvent", "kind": "Gdef", "module_public": false}, "ResponseFunctionCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_call_arguments_delta_event.ResponseFunctionCallArgumentsDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseFunctionCallArgumentsDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_call_arguments_done_event.ResponseFunctionCallArgumentsDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseImageGenCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_completed_event.ResponseImageGenCallCompletedEvent", "kind": "Gdef", "module_public": false}, "ResponseImageGenCallGeneratingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_generating_event.ResponseImageGenCallGeneratingEvent", "kind": "Gdef", "module_public": false}, "ResponseImageGenCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_in_progress_event.ResponseImageGenCallInProgressEvent", "kind": "Gdef", "module_public": false}, "ResponseImageGenCallPartialImageEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_image_gen_call_partial_image_event.ResponseImageGenCallPartialImageEvent", "kind": "Gdef", "module_public": false}, "ResponseInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_in_progress_event.ResponseInProgressEvent", "kind": "Gdef", "module_public": false}, "ResponseIncompleteEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_incomplete_event.ResponseIncompleteEvent", "kind": "Gdef", "module_public": false}, "ResponseMcpCallArgumentsDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_arguments_delta_event.ResponseMcpCallArgumentsDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseMcpCallArgumentsDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_arguments_done_event.ResponseMcpCallArgumentsDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseMcpCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_completed_event.ResponseMcpCallCompletedEvent", "kind": "Gdef", "module_public": false}, "ResponseMcpCallFailedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_failed_event.ResponseMcpCallFailedEvent", "kind": "Gdef", "module_public": false}, "ResponseMcpCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_call_in_progress_event.ResponseMcpCallInProgressEvent", "kind": "Gdef", "module_public": false}, "ResponseMcpListToolsCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_list_tools_completed_event.ResponseMcpListToolsCompletedEvent", "kind": "Gdef", "module_public": false}, "ResponseMcpListToolsFailedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_list_tools_failed_event.ResponseMcpListToolsFailedEvent", "kind": "Gdef", "module_public": false}, "ResponseMcpListToolsInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_mcp_list_tools_in_progress_event.ResponseMcpListToolsInProgressEvent", "kind": "Gdef", "module_public": false}, "ResponseOutputItemAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item_added_event.ResponseOutputItemAddedEvent", "kind": "Gdef", "module_public": false}, "ResponseOutputItemDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item_done_event.ResponseOutputItemDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseOutputTextAnnotationAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_text_annotation_added_event.ResponseOutputTextAnnotationAddedEvent", "kind": "Gdef", "module_public": false}, "ResponseQueuedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_queued_event.ResponseQueuedEvent", "kind": "Gdef", "module_public": false}, "ResponseReasoningSummaryDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_delta_event.ResponseReasoningSummaryDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseReasoningSummaryDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_done_event.ResponseReasoningSummaryDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseReasoningSummaryPartAddedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_part_added_event.ResponseReasoningSummaryPartAddedEvent", "kind": "Gdef", "module_public": false}, "ResponseReasoningSummaryPartDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_part_done_event.ResponseReasoningSummaryPartDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseReasoningSummaryTextDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_text_delta_event.ResponseReasoningSummaryTextDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseReasoningSummaryTextDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_summary_text_done_event.ResponseReasoningSummaryTextDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseRefusalDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_refusal_delta_event.ResponseRefusalDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseRefusalDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_refusal_done_event.ResponseRefusalDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseStreamEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "openai.types.responses.response_stream_event.ResponseStreamEvent", "line": 61, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["openai.types.responses.response_audio_delta_event.ResponseAudioDeltaEvent", "openai.types.responses.response_audio_done_event.ResponseAudioDoneEvent", "openai.types.responses.response_audio_transcript_delta_event.ResponseAudioTranscriptDeltaEvent", "openai.types.responses.response_audio_transcript_done_event.ResponseAudioTranscriptDoneEvent", "openai.types.responses.response_code_interpreter_call_code_delta_event.ResponseCodeInterpreterCallCodeDeltaEvent", "openai.types.responses.response_code_interpreter_call_code_done_event.ResponseCodeInterpreterCallCodeDoneEvent", "openai.types.responses.response_code_interpreter_call_completed_event.ResponseCodeInterpreterCallCompletedEvent", "openai.types.responses.response_code_interpreter_call_in_progress_event.ResponseCodeInterpreterCallInProgressEvent", "openai.types.responses.response_code_interpreter_call_interpreting_event.ResponseCodeInterpreterCallInterpretingEvent", "openai.types.responses.response_completed_event.ResponseCompletedEvent", "openai.types.responses.response_content_part_added_event.ResponseContentPartAddedEvent", "openai.types.responses.response_content_part_done_event.ResponseContentPartDoneEvent", "openai.types.responses.response_created_event.ResponseCreatedEvent", "openai.types.responses.response_error_event.ResponseErrorEvent", "openai.types.responses.response_file_search_call_completed_event.ResponseFileSearchCallCompletedEvent", "openai.types.responses.response_file_search_call_in_progress_event.ResponseFileSearchCallInProgressEvent", "openai.types.responses.response_file_search_call_searching_event.ResponseFileSearchCallSearchingEvent", "openai.types.responses.response_function_call_arguments_delta_event.ResponseFunctionCallArgumentsDeltaEvent", "openai.types.responses.response_function_call_arguments_done_event.ResponseFunctionCallArgumentsDoneEvent", "openai.types.responses.response_in_progress_event.ResponseInProgressEvent", "openai.types.responses.response_failed_event.ResponseFailedEvent", "openai.types.responses.response_incomplete_event.ResponseIncompleteEvent", "openai.types.responses.response_output_item_added_event.ResponseOutputItemAddedEvent", "openai.types.responses.response_output_item_done_event.ResponseOutputItemDoneEvent", "openai.types.responses.response_reasoning_summary_part_added_event.ResponseReasoningSummaryPartAddedEvent", "openai.types.responses.response_reasoning_summary_part_done_event.ResponseReasoningSummaryPartDoneEvent", "openai.types.responses.response_reasoning_summary_text_delta_event.ResponseReasoningSummaryTextDeltaEvent", "openai.types.responses.response_reasoning_summary_text_done_event.ResponseReasoningSummaryTextDoneEvent", "openai.types.responses.response_refusal_delta_event.ResponseRefusalDeltaEvent", "openai.types.responses.response_refusal_done_event.ResponseRefusalDoneEvent", "openai.types.responses.response_text_delta_event.ResponseTextDeltaEvent", "openai.types.responses.response_text_done_event.ResponseTextDoneEvent", "openai.types.responses.response_web_search_call_completed_event.ResponseWebSearchCallCompletedEvent", "openai.types.responses.response_web_search_call_in_progress_event.ResponseWebSearchCallInProgressEvent", "openai.types.responses.response_web_search_call_searching_event.ResponseWebSearchCallSearchingEvent", "openai.types.responses.response_image_gen_call_completed_event.ResponseImageGenCallCompletedEvent", "openai.types.responses.response_image_gen_call_generating_event.ResponseImageGenCallGeneratingEvent", "openai.types.responses.response_image_gen_call_in_progress_event.ResponseImageGenCallInProgressEvent", "openai.types.responses.response_image_gen_call_partial_image_event.ResponseImageGenCallPartialImageEvent", "openai.types.responses.response_mcp_call_arguments_delta_event.ResponseMcpCallArgumentsDeltaEvent", "openai.types.responses.response_mcp_call_arguments_done_event.ResponseMcpCallArgumentsDoneEvent", "openai.types.responses.response_mcp_call_completed_event.ResponseMcpCallCompletedEvent", "openai.types.responses.response_mcp_call_failed_event.ResponseMcpCallFailedEvent", "openai.types.responses.response_mcp_call_in_progress_event.ResponseMcpCallInProgressEvent", "openai.types.responses.response_mcp_list_tools_completed_event.ResponseMcpListToolsCompletedEvent", "openai.types.responses.response_mcp_list_tools_failed_event.ResponseMcpListToolsFailedEvent", "openai.types.responses.response_mcp_list_tools_in_progress_event.ResponseMcpListToolsInProgressEvent", "openai.types.responses.response_output_text_annotation_added_event.ResponseOutputTextAnnotationAddedEvent", "openai.types.responses.response_queued_event.ResponseQueuedEvent", "openai.types.responses.response_reasoning_summary_delta_event.ResponseReasoningSummaryDeltaEvent", "openai.types.responses.response_reasoning_summary_done_event.ResponseReasoningSummaryDoneEvent"], "uses_pep604_syntax": false}}}, "ResponseTextDeltaEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_text_delta_event.ResponseTextDeltaEvent", "kind": "Gdef", "module_public": false}, "ResponseTextDoneEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_text_done_event.ResponseTextDoneEvent", "kind": "Gdef", "module_public": false}, "ResponseWebSearchCallCompletedEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_web_search_call_completed_event.ResponseWebSearchCallCompletedEvent", "kind": "Gdef", "module_public": false}, "ResponseWebSearchCallInProgressEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_web_search_call_in_progress_event.ResponseWebSearchCallInProgressEvent", "kind": "Gdef", "module_public": false}, "ResponseWebSearchCallSearchingEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_web_search_call_searching_event.ResponseWebSearchCallSearchingEvent", "kind": "Gdef", "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.types.responses.response_stream_event.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.response_stream_event.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.response_stream_event.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.response_stream_event.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.response_stream_event.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.response_stream_event.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.response_stream_event.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\response_stream_event.py"}