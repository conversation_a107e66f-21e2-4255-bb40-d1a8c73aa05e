{".class": "MypyFile", "_fullname": "openai.lib._parsing._responses", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "FunctionToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.function_tool_param.FunctionToolParam", "kind": "Gdef"}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "NotGiven": {".class": "SymbolTableNode", "cross_ref": "openai._types.NotGiven", "kind": "Gdef"}, "PYDANTIC_V2": {".class": "SymbolTableNode", "cross_ref": "openai._compat.PYDANTIC_V2", "kind": "Gdef"}, "ParsedContent": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedContent", "kind": "Gdef"}, "ParsedResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponse", "kind": "Gdef"}, "ParsedResponseFunctionToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall", "kind": "Gdef"}, "ParsedResponseOutputItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponseOutputItem", "kind": "Gdef"}, "ParsedResponseOutputMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "kind": "Gdef"}, "ParsedResponseOutputText": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.parsed_response.ParsedResponseOutputText", "kind": "Gdef"}, "Response": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response.Response", "kind": "Gdef"}, "ResponseFormat": {".class": "SymbolTableNode", "cross_ref": "openai.types.chat.completion_create_params.ResponseFormat", "kind": "Gdef"}, "ResponseFormatTextConfigParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_format_text_config_param.ResponseFormatTextConfigParam", "kind": "Gdef"}, "ResponseFunctionToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_tool_call.ResponseFunctionToolCall", "kind": "Gdef"}, "ResponsesPydanticFunctionTool": {".class": "SymbolTableNode", "cross_ref": "openai.lib._tools.ResponsesPydanticFunctionTool", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TextFormatT": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "name": "TextFormatT", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "ToolParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.tool_param.ToolParam", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVar", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._responses.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._responses.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._responses.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._responses.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._responses.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.lib._parsing._responses.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "assert_never": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.assert_never", "kind": "Gdef"}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "construct_type_unchecked": {".class": "SymbolTableNode", "cross_ref": "openai._models.construct_type_unchecked", "kind": "Gdef"}, "get_input_tool_by_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["input_tools", "name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._responses.get_input_tool_by_name", "name": "get_input_tool_by_name", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["input_tools", "name"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.ToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "builtins.str"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_input_tool_by_name", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.function_tool_param.FunctionToolParam"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "is_basemodel_type": {".class": "SymbolTableNode", "cross_ref": "openai.lib._pydantic.is_basemodel_type", "kind": "Gdef"}, "is_dataclass_like_type": {".class": "SymbolTableNode", "cross_ref": "openai.lib._pydantic.is_dataclass_like_type", "kind": "Gdef"}, "is_given": {".class": "SymbolTableNode", "cross_ref": "openai._utils._utils.is_given", "kind": "Gdef"}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "model_parse_json": {".class": "SymbolTableNode", "cross_ref": "openai._compat.model_parse_json", "kind": "Gdef"}, "parse_function_tool_arguments": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3], "arg_names": ["input_tools", "function_call"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._responses.parse_function_tool_arguments", "name": "parse_function_tool_arguments", "type": {".class": "CallableType", "arg_kinds": [3, 3], "arg_names": ["input_tools", "function_call"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.ToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["openai.types.responses.parsed_response.ParsedResponseFunctionToolCall", "openai.types.responses.response_function_tool_call.ResponseFunctionToolCall"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_function_tool_arguments", "ret_type": "builtins.object", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "parse_response": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3], "arg_names": ["text_format", "input_tools", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._responses.parse_response", "name": "parse_response", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3], "arg_names": ["text_format", "input_tools", "response"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": -1, "name": "TextFormatT", "namespace": "openai.lib._parsing._responses.parse_response", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "openai._types.NotGiven"], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool_param.ToolParam"}], "extra_attrs": null, "type_ref": "typing.Iterable"}, "openai._types.NotGiven", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["openai.types.responses.response.Response", {".class": "Instance", "args": ["builtins.object"], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_response", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": -1, "name": "TextFormatT", "namespace": "openai.lib._parsing._responses.parse_response", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": -1, "name": "TextFormatT", "namespace": "openai.lib._parsing._responses.parse_response", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "parse_text": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["text", "text_format"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._responses.parse_text", "name": "parse_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["text", "text_format"], "arg_types": ["builtins.str", {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": -1, "name": "TextFormatT", "namespace": "openai.lib._parsing._responses.parse_text", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "openai._types.NotGiven"], "uses_pep604_syntax": true}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "parse_text", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": -1, "name": "TextFormatT", "namespace": "openai.lib._parsing._responses.parse_text", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "NoneType"}, "fullname": "openai.lib._parsing._responses.TextFormatT", "id": -1, "name": "TextFormatT", "namespace": "openai.lib._parsing._responses.parse_text", "upper_bound": "builtins.object", "values": [], "variance": 0}]}}}, "pydantic": {".class": "SymbolTableNode", "cross_ref": "pydantic", "kind": "Gdef"}, "solve_response_format_t": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.solve_response_format_t", "kind": "Gdef"}, "type_to_response_format_param": {".class": "SymbolTableNode", "cross_ref": "openai.lib._parsing._completions.type_to_response_format_param", "kind": "Gdef"}, "type_to_text_format_param": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["type_"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.lib._parsing._responses.type_to_text_format_param", "name": "type_to_text_format_param", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["type_"], "arg_types": ["builtins.type"], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "type_to_text_format_param", "ret_type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_format_text_config_param.ResponseFormatTextConfigParam"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\lib\\_parsing\\_responses.py"}