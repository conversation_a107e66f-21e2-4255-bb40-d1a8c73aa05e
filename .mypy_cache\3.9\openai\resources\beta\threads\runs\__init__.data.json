{".class": "MypyFile", "_fullname": "openai.resources.beta.threads.runs", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncRuns": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.AsyncRuns", "kind": "Gdef"}, "AsyncRunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.AsyncRunsWithRawResponse", "kind": "Gdef"}, "AsyncRunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.AsyncRunsWithStreamingResponse", "kind": "Gdef"}, "AsyncSteps": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.steps.AsyncSteps", "kind": "Gdef"}, "AsyncStepsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.steps.AsyncStepsWithRawResponse", "kind": "Gdef"}, "AsyncStepsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.steps.AsyncStepsWithStreamingResponse", "kind": "Gdef"}, "Runs": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.Runs", "kind": "Gdef"}, "RunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.RunsWithRawResponse", "kind": "Gdef"}, "RunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.RunsWithStreamingResponse", "kind": "Gdef"}, "Steps": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.steps.Steps", "kind": "Gdef"}, "StepsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.steps.StepsWithRawResponse", "kind": "Gdef"}, "StepsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.steps.StepsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.beta.threads.runs.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.runs.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.runs.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.runs.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.runs.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.runs.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.runs.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.runs.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\threads\\runs\\__init__.py"}