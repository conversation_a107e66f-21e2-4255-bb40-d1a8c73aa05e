{"data_mtime": 1754400655, "dep_lines": [18, 19, 20, 21, 13, 14, 15, 17, 18, 5, 13, 14, 3, 6, 7, 8, 13, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 10, 10, 10, 20, 5, 20, 20, 5, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["google.generativeai.types.content_types", "google.generativeai.types.generation_types", "google.generativeai.types.helper_types", "google.generativeai.types.safety_types", "google.api_core.exceptions", "google.generativeai.protos", "google.generativeai.client", "google.generativeai.caching", "google.generativeai.types", "collections.abc", "google.api_core", "google.generativeai", "__future__", "textwrap", "typing", "reprlib", "google", "builtins", "_frozen_importlib", "abc", "contextlib", "google.ai", "google.ai.generativelanguage_v1beta", "google.ai.generativelanguage_v1beta.services", "google.ai.generativelanguage_v1beta.services.generative_service", "google.ai.generativelanguage_v1beta.services.generative_service.async_client", "google.ai.generativelanguage_v1beta.services.generative_service.client", "google.ai.generativelanguage_v1beta.types", "google.ai.generativelanguage_v1beta.types.content", "google.ai.generativelanguage_v1beta.types.file", "google.ai.generativelanguage_v1beta.types.generative_service", "google.ai.generativelanguage_v1beta.types.safety", "google.api_core.retry", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.api_core.timeout", "google.generativeai.types.file_types"], "hash": "522a86bf1d7c34b9ee54ad463ea8efeed1721ccc", "id": "google.generativeai.generative_models", "ignore_all": true, "interface_hash": "2d7ef23f615e40e50ed012796d642ffd2c98b4d7", "mtime": 1754241460, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\generativeai\\generative_models.py", "plugin_data": null, "size": 33828, "suppressed": [], "version_id": "1.17.1"}