{".class": "MypyFile", "_fullname": "google.ai.generativelanguage_v1beta", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AttributionSourceId": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.AttributionSourceId", "kind": "Gdef"}, "BatchCreateChunksRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.BatchCreateChunksRequest", "kind": "Gdef"}, "BatchCreateChunksResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.BatchCreateChunksResponse", "kind": "Gdef"}, "BatchDeleteChunksRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.BatchDeleteChunksRequest", "kind": "Gdef"}, "BatchEmbedContentsRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.BatchEmbedContentsRequest", "kind": "Gdef"}, "BatchEmbedContentsResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.BatchEmbedContentsResponse", "kind": "Gdef"}, "BatchEmbedTextRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextRequest", "kind": "Gdef"}, "BatchEmbedTextResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextResponse", "kind": "Gdef"}, "BatchUpdateChunksRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.BatchUpdateChunksRequest", "kind": "Gdef"}, "BatchUpdateChunksResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.BatchUpdateChunksResponse", "kind": "Gdef"}, "Blob": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.Blob", "kind": "Gdef"}, "CacheServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.cache_service.async_client.CacheServiceAsyncClient", "kind": "Gdef"}, "CacheServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.cache_service.client.CacheServiceClient", "kind": "Gdef"}, "CachedContent": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.cached_content.CachedContent", "kind": "Gdef"}, "Candidate": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.Candidate", "kind": "Gdef"}, "Chunk": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever.Chunk", "kind": "Gdef"}, "ChunkData": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever.ChunkData", "kind": "Gdef"}, "CitationMetadata": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.citation.CitationMetadata", "kind": "Gdef"}, "CitationSource": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.citation.CitationSource", "kind": "Gdef"}, "CodeExecution": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.CodeExecution", "kind": "Gdef"}, "CodeExecutionResult": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.CodeExecutionResult", "kind": "Gdef"}, "Condition": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever.Condition", "kind": "Gdef"}, "Content": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.Content", "kind": "Gdef"}, "ContentEmbedding": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.ContentEmbedding", "kind": "Gdef"}, "ContentFilter": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.safety.ContentFilter", "kind": "Gdef"}, "Corpus": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever.Corpus", "kind": "Gdef"}, "CountMessageTokensRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.discuss_service.CountMessageTokensRequest", "kind": "Gdef"}, "CountMessageTokensResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.discuss_service.CountMessageTokensResponse", "kind": "Gdef"}, "CountTextTokensRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensRequest", "kind": "Gdef"}, "CountTextTokensResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensResponse", "kind": "Gdef"}, "CountTokensRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.CountTokensRequest", "kind": "Gdef"}, "CountTokensResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.CountTokensResponse", "kind": "Gdef"}, "CreateCachedContentRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.cache_service.CreateCachedContentRequest", "kind": "Gdef"}, "CreateChunkRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.CreateChunkRequest", "kind": "Gdef"}, "CreateCorpusRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.CreateCorpusRequest", "kind": "Gdef"}, "CreateDocumentRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.CreateDocumentRequest", "kind": "Gdef"}, "CreateFileRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.file_service.CreateFileRequest", "kind": "Gdef"}, "CreateFileResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.file_service.CreateFileResponse", "kind": "Gdef"}, "CreatePermissionRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.permission_service.CreatePermissionRequest", "kind": "Gdef"}, "CreateTunedModelMetadata": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.model_service.CreateTunedModelMetadata", "kind": "Gdef"}, "CreateTunedModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.model_service.CreateTunedModelRequest", "kind": "Gdef"}, "CustomMetadata": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever.CustomMetadata", "kind": "Gdef"}, "Dataset": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.tuned_model.Dataset", "kind": "Gdef"}, "DeleteCachedContentRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.cache_service.DeleteCachedContentRequest", "kind": "Gdef"}, "DeleteChunkRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.DeleteChunkRequest", "kind": "Gdef"}, "DeleteCorpusRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.DeleteCorpusRequest", "kind": "Gdef"}, "DeleteDocumentRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.DeleteDocumentRequest", "kind": "Gdef"}, "DeleteFileRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.file_service.DeleteFileRequest", "kind": "Gdef"}, "DeletePermissionRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.permission_service.DeletePermissionRequest", "kind": "Gdef"}, "DeleteTunedModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.model_service.DeleteTunedModelRequest", "kind": "Gdef"}, "DiscussServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.discuss_service.async_client.DiscussServiceAsyncClient", "kind": "Gdef"}, "DiscussServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.discuss_service.client.DiscussServiceClient", "kind": "Gdef"}, "Document": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever.Document", "kind": "Gdef"}, "DynamicRetrievalConfig": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.DynamicRetrievalConfig", "kind": "Gdef"}, "EmbedContentRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.EmbedContentRequest", "kind": "Gdef"}, "EmbedContentResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.EmbedContentResponse", "kind": "Gdef"}, "EmbedTextRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service.EmbedTextRequest", "kind": "Gdef"}, "EmbedTextResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service.EmbedTextResponse", "kind": "Gdef"}, "Embedding": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service.Embedding", "kind": "Gdef"}, "Example": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.discuss_service.Example", "kind": "Gdef"}, "ExecutableCode": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.ExecutableCode", "kind": "Gdef"}, "File": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.file.File", "kind": "Gdef"}, "FileData": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.FileData", "kind": "Gdef"}, "FileServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.file_service.async_client.FileServiceAsyncClient", "kind": "Gdef"}, "FileServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.file_service.client.FileServiceClient", "kind": "Gdef"}, "FunctionCall": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.FunctionCall", "kind": "Gdef"}, "FunctionCallingConfig": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.FunctionCallingConfig", "kind": "Gdef"}, "FunctionDeclaration": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.FunctionDeclaration", "kind": "Gdef"}, "FunctionResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.FunctionResponse", "kind": "Gdef"}, "GenerateAnswerRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.GenerateAnswerRequest", "kind": "Gdef"}, "GenerateAnswerResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.GenerateAnswerResponse", "kind": "Gdef"}, "GenerateContentRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentRequest", "kind": "Gdef"}, "GenerateContentResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.GenerateContentResponse", "kind": "Gdef"}, "GenerateMessageRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.discuss_service.GenerateMessageRequest", "kind": "Gdef"}, "GenerateMessageResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.discuss_service.GenerateMessageResponse", "kind": "Gdef"}, "GenerateTextRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service.GenerateTextRequest", "kind": "Gdef"}, "GenerateTextResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service.GenerateTextResponse", "kind": "Gdef"}, "GenerationConfig": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.GenerationConfig", "kind": "Gdef"}, "GenerativeServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.generative_service.async_client.GenerativeServiceAsyncClient", "kind": "Gdef"}, "GenerativeServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.generative_service.client.GenerativeServiceClient", "kind": "Gdef"}, "GetCachedContentRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.cache_service.GetCachedContentRequest", "kind": "Gdef"}, "GetChunkRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.GetChunkRequest", "kind": "Gdef"}, "GetCorpusRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.GetCorpusRequest", "kind": "Gdef"}, "GetDocumentRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.GetDocumentRequest", "kind": "Gdef"}, "GetFileRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.file_service.GetFileRequest", "kind": "Gdef"}, "GetModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.model_service.GetModelRequest", "kind": "Gdef"}, "GetPermissionRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.permission_service.GetPermissionRequest", "kind": "Gdef"}, "GetTunedModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.model_service.GetTunedModelRequest", "kind": "Gdef"}, "GoogleSearchRetrieval": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.GoogleSearchRetrieval", "kind": "Gdef"}, "GroundingAttribution": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.GroundingAttribution", "kind": "Gdef"}, "GroundingChunk": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.GroundingChunk", "kind": "Gdef"}, "GroundingMetadata": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.GroundingMetadata", "kind": "Gdef"}, "GroundingPassage": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.GroundingPassage", "kind": "Gdef"}, "GroundingPassages": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.GroundingPassages", "kind": "Gdef"}, "GroundingSupport": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.GroundingSupport", "kind": "Gdef"}, "HarmCategory": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.safety.HarmCategory", "kind": "Gdef"}, "Hyperparameters": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.tuned_model.Hyperparameters", "kind": "Gdef"}, "ListCachedContentsRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.cache_service.ListCachedContentsRequest", "kind": "Gdef"}, "ListCachedContentsResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.cache_service.ListCachedContentsResponse", "kind": "Gdef"}, "ListChunksRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.ListChunksRequest", "kind": "Gdef"}, "ListChunksResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.ListChunksResponse", "kind": "Gdef"}, "ListCorporaRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.ListCorporaRequest", "kind": "Gdef"}, "ListCorporaResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.ListCorporaResponse", "kind": "Gdef"}, "ListDocumentsRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.ListDocumentsRequest", "kind": "Gdef"}, "ListDocumentsResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.ListDocumentsResponse", "kind": "Gdef"}, "ListFilesRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.file_service.ListFilesRequest", "kind": "Gdef"}, "ListFilesResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.file_service.ListFilesResponse", "kind": "Gdef"}, "ListModelsRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.model_service.ListModelsRequest", "kind": "Gdef"}, "ListModelsResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.model_service.ListModelsResponse", "kind": "Gdef"}, "ListPermissionsRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsRequest", "kind": "Gdef"}, "ListPermissionsResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.permission_service.ListPermissionsResponse", "kind": "Gdef"}, "ListTunedModelsRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.model_service.ListTunedModelsRequest", "kind": "Gdef"}, "ListTunedModelsResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.model_service.ListTunedModelsResponse", "kind": "Gdef"}, "LogprobsResult": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.LogprobsResult", "kind": "Gdef"}, "Message": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.discuss_service.Message", "kind": "Gdef"}, "MessagePrompt": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.discuss_service.MessagePrompt", "kind": "Gdef"}, "MetadataFilter": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever.MetadataFilter", "kind": "Gdef"}, "Model": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.model.Model", "kind": "Gdef"}, "ModelServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.model_service.async_client.ModelServiceAsyncClient", "kind": "Gdef"}, "ModelServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.model_service.client.ModelServiceClient", "kind": "Gdef"}, "Part": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.Part", "kind": "Gdef"}, "Permission": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.permission.Permission", "kind": "Gdef"}, "PermissionServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.permission_service.async_client.PermissionServiceAsyncClient", "kind": "Gdef"}, "PermissionServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.permission_service.client.PermissionServiceClient", "kind": "Gdef"}, "PrebuiltVoiceConfig": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.PrebuiltVoiceConfig", "kind": "Gdef"}, "PredictRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.prediction_service.PredictRequest", "kind": "Gdef"}, "PredictResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.prediction_service.PredictResponse", "kind": "Gdef"}, "PredictionServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.prediction_service.async_client.PredictionServiceAsyncClient", "kind": "Gdef"}, "PredictionServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.prediction_service.client.PredictionServiceClient", "kind": "Gdef"}, "QueryCorpusRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.QueryCorpusRequest", "kind": "Gdef"}, "QueryCorpusResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.QueryCorpusResponse", "kind": "Gdef"}, "QueryDocumentRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.QueryDocumentRequest", "kind": "Gdef"}, "QueryDocumentResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.QueryDocumentResponse", "kind": "Gdef"}, "RelevantChunk": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.RelevantChunk", "kind": "Gdef"}, "RetrievalMetadata": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.RetrievalMetadata", "kind": "Gdef"}, "RetrieverServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.retriever_service.async_client.RetrieverServiceAsyncClient", "kind": "Gdef"}, "RetrieverServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.retriever_service.client.RetrieverServiceClient", "kind": "Gdef"}, "SafetyFeedback": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.safety.SafetyFeedback", "kind": "Gdef"}, "SafetyRating": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.safety.SafetyRating", "kind": "Gdef"}, "SafetySetting": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.safety.SafetySetting", "kind": "Gdef"}, "Schema": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.Schema", "kind": "Gdef"}, "SearchEntryPoint": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.SearchEntryPoint", "kind": "Gdef"}, "Segment": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.Segment", "kind": "Gdef"}, "SemanticRetrieverConfig": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.SemanticRetrieverConfig", "kind": "Gdef"}, "SpeechConfig": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.SpeechConfig", "kind": "Gdef"}, "StringList": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever.StringList", "kind": "Gdef"}, "TaskType": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.TaskType", "kind": "Gdef"}, "TextCompletion": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service.TextCompletion", "kind": "Gdef"}, "TextPrompt": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service.TextPrompt", "kind": "Gdef"}, "TextServiceAsyncClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.text_service.async_client.TextServiceAsyncClient", "kind": "Gdef"}, "TextServiceClient": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.text_service.client.TextServiceClient", "kind": "Gdef"}, "Tool": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.Tool", "kind": "Gdef"}, "ToolConfig": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.ToolConfig", "kind": "Gdef"}, "TransferOwnershipRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.permission_service.TransferOwnershipRequest", "kind": "Gdef"}, "TransferOwnershipResponse": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.permission_service.TransferOwnershipResponse", "kind": "Gdef"}, "TunedModel": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.tuned_model.TunedModel", "kind": "Gdef"}, "TunedModelSource": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.tuned_model.TunedModelSource", "kind": "Gdef"}, "TuningExample": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.tuned_model.TuningExample", "kind": "Gdef"}, "TuningExamples": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.tuned_model.TuningExamples", "kind": "Gdef"}, "TuningSnapshot": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.tuned_model.TuningSnapshot", "kind": "Gdef"}, "TuningTask": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.tuned_model.TuningTask", "kind": "Gdef"}, "Type": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.content.Type", "kind": "Gdef"}, "UpdateCachedContentRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.cache_service.UpdateCachedContentRequest", "kind": "Gdef"}, "UpdateChunkRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.UpdateChunkRequest", "kind": "Gdef"}, "UpdateCorpusRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.UpdateCorpusRequest", "kind": "Gdef"}, "UpdateDocumentRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.retriever_service.UpdateDocumentRequest", "kind": "Gdef"}, "UpdatePermissionRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.permission_service.UpdatePermissionRequest", "kind": "Gdef"}, "UpdateTunedModelRequest": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.model_service.UpdateTunedModelRequest", "kind": "Gdef"}, "VideoMetadata": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.file.VideoMetadata", "kind": "Gdef"}, "VoiceConfig": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.generative_service.VoiceConfig", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "__version__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.__version__", "name": "__version__", "setter_type": null, "type": "builtins.str"}}, "package_version": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.gapic_version", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\__init__.py"}