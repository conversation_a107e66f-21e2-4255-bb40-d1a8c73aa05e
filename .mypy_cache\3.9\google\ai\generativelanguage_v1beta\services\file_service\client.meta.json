{"data_mtime": 1754400654, "dep_lines": [68, 69, 70, 71, 65, 65, 66, 66, 41, 42, 45, 66, 35, 36, 37, 38, 39, 40, 41, 43, 45, 53, 623, 35, 39, 43, 16, 17, 18, 19, 20, 33, 623, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 61, 62, 63], "dep_prios": [5, 5, 5, 5, 10, 20, 10, 10, 10, 5, 10, 20, 10, 10, 10, 10, 10, 5, 20, 10, 20, 10, 20, 20, 20, 20, 5, 10, 10, 10, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 5, 5, 5], "dependencies": ["google.ai.generativelanguage_v1beta.services.file_service.transports.base", "google.ai.generativelanguage_v1beta.services.file_service.transports.grpc", "google.ai.generativelanguage_v1beta.services.file_service.transports.grpc_asyncio", "google.ai.generativelanguage_v1beta.services.file_service.transports.rest", "google.ai.generativelanguage_v1beta.services.file_service.pagers", "google.ai.generativelanguage_v1beta.services.file_service", "google.ai.generativelanguage_v1beta.types.file", "google.ai.generativelanguage_v1beta.types.file_service", "google.auth.transport.mtls", "google.auth.transport.grpc", "google.ai.generativelanguage_v1beta.gapic_version", "google.ai.generativelanguage_v1beta.types", "google.api_core.client_options", "google.api_core.exceptions", "google.api_core.gapic_v1", "google.api_core.retry", "google.auth.credentials", "google.auth.exceptions", "google.auth.transport", "google.oauth2.service_account", "google.ai.generativelanguage_v1beta", "google.api_core.client_logging", "google.auth._default", "google.api_core", "google.auth", "google.oauth2", "collections", "logging", "os", "re", "typing", "warnings", "google", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "google.ai.generativelanguage_v1beta.services.file_service.transports", "google.ai.generativelanguage_v1beta.services.file_service.transports.rest_base", "google.api_core.client_info", "google.api_core.gapic_v1.client_info", "google.api_core.gapic_v1.method", "google.api_core.gapic_v1.routing_header", "google.api_core.retry.retry_base", "google.api_core.retry.retry_unary", "google.auth._credentials_base", "types", "typing_extensions"], "hash": "154618b8517367eb93442f199fae083f7906ae6a", "id": "google.ai.generativelanguage_v1beta.services.file_service.client", "ignore_all": true, "interface_hash": "****************************************", "mtime": **********, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\file_service\\client.py", "plugin_data": null, "size": 47558, "suppressed": ["google.longrunning", "google.protobuf", "google.rpc"], "version_id": "1.17.1"}