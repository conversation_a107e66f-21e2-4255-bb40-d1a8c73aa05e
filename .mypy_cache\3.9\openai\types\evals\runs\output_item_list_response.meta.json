{"data_mtime": 1754400648, "dep_lines": [8, 7, 3, 4, 5, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.types.evals.eval_api_error", "openai._models", "builtins", "typing", "typing_extensions", "_frozen_importlib", "abc", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "bb77cc8ee606f9a8d989ff9df44f026a0be8f9fa", "id": "openai.types.evals.runs.output_item_list_response", "ignore_all": true, "interface_hash": "083c043adc70a70eada8961a9ff7c1eddf36af23", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\runs\\output_item_list_response.py", "plugin_data": null, "size": 2747, "suppressed": [], "version_id": "1.17.1"}