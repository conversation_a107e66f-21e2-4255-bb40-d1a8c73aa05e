{".class": "MypyFile", "_fullname": "openai.resources.responses", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncInputItems": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.input_items.AsyncInputItems", "kind": "Gdef"}, "AsyncInputItemsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.input_items.AsyncInputItemsWithRawResponse", "kind": "Gdef"}, "AsyncInputItemsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.input_items.AsyncInputItemsWithStreamingResponse", "kind": "Gdef"}, "AsyncResponses": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.responses.AsyncResponses", "kind": "Gdef"}, "AsyncResponsesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.responses.AsyncResponsesWithRawResponse", "kind": "Gdef"}, "AsyncResponsesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.responses.AsyncResponsesWithStreamingResponse", "kind": "Gdef"}, "InputItems": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.input_items.InputItems", "kind": "Gdef"}, "InputItemsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.input_items.InputItemsWithRawResponse", "kind": "Gdef"}, "InputItemsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.input_items.InputItemsWithStreamingResponse", "kind": "Gdef"}, "Responses": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.responses.Responses", "kind": "Gdef"}, "ResponsesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.responses.ResponsesWithRawResponse", "kind": "Gdef"}, "ResponsesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.responses.responses.ResponsesWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.responses.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.responses.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.responses.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.responses.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.responses.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.responses.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.responses.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.responses.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\responses\\__init__.py"}