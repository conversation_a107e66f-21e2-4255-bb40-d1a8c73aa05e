{".class": "MypyFile", "_fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef", "module_public": false}, "AuthorizedSession": {".class": "SymbolTableNode", "cross_ref": "google.auth.transport.requests.AuthorizedSession", "kind": "Gdef", "module_public": false}, "BASE_DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.text_service.transports.base.DEFAULT_CLIENT_INFO", "kind": "Gdef", "module_public": false}, "CLIENT_LOGGING_SUPPORTED": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.CLIENT_LOGGING_SUPPORTED", "name": "CLIENT_LOGGING_SUPPORTED", "setter_type": null, "type": "builtins.bool"}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef", "module_public": false}, "DEFAULT_CLIENT_INFO": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.DEFAULT_CLIENT_INFO", "name": "DEFAULT_CLIENT_INFO", "setter_type": null, "type": "google.api_core.gapic_v1.client_info.ClientInfo"}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "OptionalRetry": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.OptionalRetry", "line": 37, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["google.api_core.retry.retry_unary.Retry", "google.api_core.gapic_v1.method._MethodDefault", {".class": "NoneType"}], "uses_pep604_syntax": false}}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef", "module_public": false}, "TextServiceRestInterceptor": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "name": "TextServiceRestInterceptor", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest", "mro": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "builtins.object"], "names": {".class": "SymbolTable", "post_batch_embed_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.post_batch_embed_text", "name": "post_batch_embed_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_batch_embed_text of TextServiceRestInterceptor", "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_count_text_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.post_count_text_tokens", "name": "post_count_text_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_count_text_tokens of TextServiceRestInterceptor", "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_embed_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.post_embed_text", "name": "post_embed_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "google.ai.generativelanguage_v1beta.types.text_service.EmbedTextResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_embed_text of TextServiceRestInterceptor", "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.EmbedTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_generate_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.post_generate_text", "name": "post_generate_text", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "google.ai.generativelanguage_v1beta.types.text_service.GenerateTextResponse"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_generate_text of TextServiceRestInterceptor", "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.GenerateTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.post_get_operation", "name": "post_get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_get_operation of TextServiceRestInterceptor", "ret_type": {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "post_list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "response"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.post_list_operations", "name": "post_list_operations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "response"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "post_list_operations of TextServiceRestInterceptor", "ret_type": {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_batch_embed_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.pre_batch_embed_text", "name": "pre_batch_embed_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_batch_embed_text of TextServiceRestInterceptor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_count_text_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.pre_count_text_tokens", "name": "pre_count_text_tokens", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_count_text_tokens of TextServiceRestInterceptor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_embed_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.pre_embed_text", "name": "pre_embed_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "google.ai.generativelanguage_v1beta.types.text_service.EmbedTextRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_embed_text of TextServiceRestInterceptor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["google.ai.generativelanguage_v1beta.types.text_service.EmbedTextRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_generate_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.pre_generate_text", "name": "pre_generate_text", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "google.ai.generativelanguage_v1beta.types.text_service.GenerateTextRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_generate_text of TextServiceRestInterceptor", "ret_type": {".class": "TupleType", "implicit": false, "items": ["google.ai.generativelanguage_v1beta.types.text_service.GenerateTextRequest", {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.pre_get_operation", "name": "pre_get_operation", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_get_operation of TextServiceRestInterceptor", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pre_list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.pre_list_operations", "name": "pre_list_operations", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "request", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "pre_list_operations of TextServiceRestInterceptor", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextServiceRestStub": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub", "name": "TextServiceRestStub", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 261, "name": "_session", "type": "google.auth.transport.requests.AuthorizedSession"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 262, "name": "_host", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 263, "name": "_interceptor", "type": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest", "mro": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_session", "_host", "_interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "_session", "_host", "_interceptor"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub", "google.auth.transport.requests.AuthorizedSession", "builtins.str", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TextServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "arg_types": ["google.auth.transport.requests.AuthorizedSession", "builtins.str", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of TextServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5], "arg_names": ["_session", "_host", "_interceptor"], "arg_types": ["google.auth.transport.requests.AuthorizedSession", "builtins.str", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of TextServiceRestStub", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "_host": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub._host", "name": "_host", "setter_type": null, "type": "builtins.str"}}, "_interceptor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub._interceptor", "name": "_interceptor", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor"}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub._session", "name": "_session", "setter_type": null, "type": "google.auth.transport.requests.AuthorizedSession"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TextServiceRestTransport": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport", "name": "TextServiceRestTransport", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest", "mro": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport", "google.ai.generativelanguage_v1beta.services.text_service.transports.base.TextServiceTransport", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "_BatchEmbedText": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseBatchEmbedText", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._BatchEmbedText", "name": "_BatchEmbedText", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._BatchEmbedText", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest", "mro": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._BatchEmbedText", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseBatchEmbedText", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._BatchEmbedText.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._BatchEmbedText", "google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _BatchEmbedText", "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._BatchEmbedText.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._BatchEmbedText._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._BatchEmbedText._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _BatchEmbedText", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._BatchEmbedText.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._BatchEmbedText", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_CountTextTokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseCountTextTokens", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._CountTextTokens", "name": "_CountTextTokens", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._CountTextTokens", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest", "mro": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._CountTextTokens", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseCountTextTokens", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._CountTextTokens.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._CountTextTokens", "google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _CountTextTokens", "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._CountTextTokens.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._CountTextTokens._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._CountTextTokens._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _CountTextTokens", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._CountTextTokens.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._CountTextTokens", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_EmbedText": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseEmbedText", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._EmbedText", "name": "_EmbedText", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._EmbedText", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest", "mro": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._EmbedText", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseEmbedText", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._EmbedText.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._EmbedText", "google.ai.generativelanguage_v1beta.types.text_service.EmbedTextRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _EmbedText", "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.EmbedTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._EmbedText.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._EmbedText._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._EmbedText._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _EmbedText", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._EmbedText.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._EmbedText", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GenerateText": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseGenerateText", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GenerateText", "name": "_GenerateText", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GenerateText", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest", "mro": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GenerateText", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseGenerateText", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GenerateText.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GenerateText", "google.ai.generativelanguage_v1beta.types.text_service.GenerateTextRequest", {".class": "TypeAliasType", "args": [], "type_ref": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GenerateText", "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.GenerateTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GenerateText.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GenerateText._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GenerateText._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GenerateText", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GenerateText.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GenerateText", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_GetOperation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseGetOperation", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GetOperation", "name": "_GetOperation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GetOperation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest", "mro": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GetOperation", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseGetOperation", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GetOperation.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GetOperation", {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _GetOperation", "ret_type": {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GetOperation.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GetOperation._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GetOperation._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _GetOperation", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GetOperation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._GetOperation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "_ListOperations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseListOperations", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._ListOperations", "name": "_ListOperations", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._ListOperations", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest", "mro": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._ListOperations", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport._BaseListOperations", "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestStub", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._ListOperations.__call__", "name": "__call__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5, 5], "arg_names": ["self", "request", "retry", "timeout", "metadata"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._ListOperations", {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}, {".class": "TypeAliasType", "args": [], "type_ref": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.OptionalRetry"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", "builtins.bytes"], "uses_pep604_syntax": false}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "typing.Sequence"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__call__ of _ListOperations", "ret_type": {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__hash__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._ListOperations.__hash__", "name": "__hash__", "type": null}}, "_get_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._ListOperations._get_response", "name": "_get_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._ListOperations._get_response", "name": "_get_response", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 1], "arg_names": ["host", "metadata", "query_params", "session", "timeout", "transcoded_request", "body"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "_get_response of _ListOperations", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._ListOperations.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._ListOperations", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "credentials_file", "scopes", "client_cert_source_for_mtls", "quota_project_id", "client_info", "always_use_jwt_access", "url_scheme", "interceptor", "api_audience"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "host", "credentials", "credentials_file", "scopes", "client_cert_source_for_mtls", "quota_project_id", "client_info", "always_use_jwt_access", "url_scheme", "interceptor", "api_audience"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport", "builtins.str", {".class": "UnionType", "items": ["google.auth.credentials.Credentials", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [], "arg_names": [], "arg_types": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.bytes", "builtins.bytes"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "google.api_core.gapic_v1.client_info.ClientInfo", {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "UnionType", "items": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of TextServiceRestTransport", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_interceptor": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._interceptor", "name": "_interceptor", "setter_type": null, "type": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestInterceptor"}}, "_session": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport._session", "name": "_session", "setter_type": null, "type": "google.auth.transport.requests.AuthorizedSession"}}, "batch_embed_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.batch_embed_text", "name": "batch_embed_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch_embed_text of TextServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.batch_embed_text", "name": "batch_embed_text", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "batch_embed_text of TextServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.BatchEmbedTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "close": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.close", "name": "close", "type": null}}, "count_text_tokens": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.count_text_tokens", "name": "count_text_tokens", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count_text_tokens of TextServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.count_text_tokens", "name": "count_text_tokens", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "count_text_tokens of TextServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.CountTextTokensResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "embed_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.embed_text", "name": "embed_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_text of TextServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.text_service.EmbedTextRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.EmbedTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.embed_text", "name": "embed_text", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "embed_text of TextServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.text_service.EmbedTextRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.EmbedTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "generate_text": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.generate_text", "name": "generate_text", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate_text of TextServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.text_service.GenerateTextRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.GenerateTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.generate_text", "name": "generate_text", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "generate_text of TextServiceRestTransport", "ret_type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["google.ai.generativelanguage_v1beta.types.text_service.GenerateTextRequest"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": null, "ret_type": "google.ai.generativelanguage_v1beta.types.text_service.GenerateTextResponse", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "get_operation": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.get_operation", "name": "get_operation", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.get_operation", "name": "get_operation", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "get_operation of TextServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "kind": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.kind", "name": "kind", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of TextServiceRestTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.kind", "name": "kind", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "kind of TextServiceRestTransport", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "list_operations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.list_operations", "name": "list_operations", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.list_operations", "name": "list_operations", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport"], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "list_operations of TextServiceRestTransport", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.TextServiceRestTransport", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "_BaseTextServiceRestTransport": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest_base._BaseTextServiceRestTransport", "kind": "Gdef", "module_public": false}, "_LOGGER": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest._LOGGER", "name": "_LOGGER", "setter_type": null, "type": "logging.Logger"}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.__all__", "name": "__all__", "setter_type": null, "type": {".class": "TupleType", "implicit": false, "items": ["builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "client_logging": {".class": "SymbolTableNode", "cross_ref": "google.api_core.client_logging", "kind": "Gdef", "module_public": false}, "core_exceptions": {".class": "SymbolTableNode", "cross_ref": "google.api_core.exceptions", "kind": "Gdef", "module_public": false}, "dataclasses": {".class": "SymbolTableNode", "cross_ref": "dataclasses", "kind": "Gdef", "module_public": false}, "ga_credentials": {".class": "SymbolTableNode", "cross_ref": "google.auth.credentials", "kind": "Gdef", "module_public": false}, "gapic_v1": {".class": "SymbolTableNode", "cross_ref": "google.api_core.gapic_v1", "kind": "Gdef", "module_public": false}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef", "module_public": false}, "json_format": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.json_format", "name": "json_format", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.json_format", "source_any": null, "type_of_any": 3}}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef", "module_public": false}, "operations_pb2": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "name": "operations_pb2", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.operations_pb2", "source_any": null, "type_of_any": 3}}}, "requests_version": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_suppressed_import", "is_ready", "is_inferred"], "fullname": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.requests_version", "name": "requests_version", "setter_type": null, "type": {".class": "AnyType", "missing_import_name": "google.ai.generativelanguage_v1beta.services.text_service.transports.rest.requests_version", "source_any": null, "type_of_any": 3}}}, "rest_helpers": {".class": "SymbolTableNode", "cross_ref": "google.api_core.rest_helpers", "kind": "Gdef", "module_public": false}, "rest_streaming": {".class": "SymbolTableNode", "cross_ref": "google.api_core.rest_streaming", "kind": "Gdef", "module_public": false}, "retries": {".class": "SymbolTableNode", "cross_ref": "google.api_core.retry", "kind": "Gdef", "module_public": false}, "text_service": {".class": "SymbolTableNode", "cross_ref": "google.ai.generativelanguage_v1beta.types.text_service", "kind": "Gdef", "module_public": false}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef", "module_public": false}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\google\\ai\\generativelanguage_v1beta\\services\\text_service\\transports\\rest.py"}