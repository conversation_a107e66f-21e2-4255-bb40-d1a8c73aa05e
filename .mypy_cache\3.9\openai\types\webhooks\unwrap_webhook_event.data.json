{".class": "MypyFile", "_fullname": "openai.types.webhooks.unwrap_webhook_event", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef", "module_public": false}, "BatchCancelledWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.batch_cancelled_webhook_event.BatchCancelledWebhookEvent", "kind": "Gdef", "module_public": false}, "BatchCompletedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.batch_completed_webhook_event.BatchCompletedWebhookEvent", "kind": "Gdef", "module_public": false}, "BatchExpiredWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.batch_expired_webhook_event.BatchExpiredWebhookEvent", "kind": "Gdef", "module_public": false}, "BatchFailedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.batch_failed_webhook_event.BatchFailedWebhookEvent", "kind": "Gdef", "module_public": false}, "EvalRunCanceledWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.eval_run_canceled_webhook_event.EvalRunCanceledWebhookEvent", "kind": "Gdef", "module_public": false}, "EvalRunFailedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.eval_run_failed_webhook_event.EvalRunFailedWebhookEvent", "kind": "Gdef", "module_public": false}, "EvalRunSucceededWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.eval_run_succeeded_webhook_event.EvalRunSucceededWebhookEvent", "kind": "Gdef", "module_public": false}, "FineTuningJobCancelledWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.fine_tuning_job_cancelled_webhook_event.FineTuningJobCancelledWebhookEvent", "kind": "Gdef", "module_public": false}, "FineTuningJobFailedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.fine_tuning_job_failed_webhook_event.FineTuningJobFailedWebhookEvent", "kind": "Gdef", "module_public": false}, "FineTuningJobSucceededWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.fine_tuning_job_succeeded_webhook_event.FineTuningJobSucceededWebhookEvent", "kind": "Gdef", "module_public": false}, "PropertyInfo": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.PropertyInfo", "kind": "Gdef", "module_public": false}, "ResponseCancelledWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.response_cancelled_webhook_event.ResponseCancelledWebhookEvent", "kind": "Gdef", "module_public": false}, "ResponseCompletedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.response_completed_webhook_event.ResponseCompletedWebhookEvent", "kind": "Gdef", "module_public": false}, "ResponseFailedWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.response_failed_webhook_event.ResponseFailedWebhookEvent", "kind": "Gdef", "module_public": false}, "ResponseIncompleteWebhookEvent": {".class": "SymbolTableNode", "cross_ref": "openai.types.webhooks.response_incomplete_webhook_event.ResponseIncompleteWebhookEvent", "kind": "Gdef", "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "UnwrapWebhookEvent": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 0, "fullname": "openai.types.webhooks.unwrap_webhook_event.UnwrapWebhookEvent", "line": 24, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": ["openai.types.webhooks.batch_cancelled_webhook_event.BatchCancelledWebhookEvent", "openai.types.webhooks.batch_completed_webhook_event.BatchCompletedWebhookEvent", "openai.types.webhooks.batch_expired_webhook_event.BatchExpiredWebhookEvent", "openai.types.webhooks.batch_failed_webhook_event.BatchFailedWebhookEvent", "openai.types.webhooks.eval_run_canceled_webhook_event.EvalRunCanceledWebhookEvent", "openai.types.webhooks.eval_run_failed_webhook_event.EvalRunFailedWebhookEvent", "openai.types.webhooks.eval_run_succeeded_webhook_event.EvalRunSucceededWebhookEvent", "openai.types.webhooks.fine_tuning_job_cancelled_webhook_event.FineTuningJobCancelledWebhookEvent", "openai.types.webhooks.fine_tuning_job_failed_webhook_event.FineTuningJobFailedWebhookEvent", "openai.types.webhooks.fine_tuning_job_succeeded_webhook_event.FineTuningJobSucceededWebhookEvent", "openai.types.webhooks.response_cancelled_webhook_event.ResponseCancelledWebhookEvent", "openai.types.webhooks.response_completed_webhook_event.ResponseCompletedWebhookEvent", "openai.types.webhooks.response_failed_webhook_event.ResponseFailedWebhookEvent", "openai.types.webhooks.response_incomplete_webhook_event.ResponseIncompleteWebhookEvent"], "uses_pep604_syntax": false}}}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.types.webhooks.unwrap_webhook_event.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.unwrap_webhook_event.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.unwrap_webhook_event.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.unwrap_webhook_event.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.unwrap_webhook_event.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.unwrap_webhook_event.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.webhooks.unwrap_webhook_event.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\webhooks\\unwrap_webhook_event.py"}