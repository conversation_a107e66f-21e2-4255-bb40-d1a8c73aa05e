{"data_mtime": 1754400651, "dep_lines": [23, 13, 50, 35, 35, 35, 49, 51, 52, 53, 54, 55, 56, 57, 35, 41, 12, 21, 22, 31, 32, 33, 34, 40, 3, 5, 6, 7, 10, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["openai.resources.beta.threads.runs.runs", "openai.resources.beta.threads.messages", "openai.types.beta.threads.run", "openai.types.beta.thread_create_params", "openai.types.beta.thread_update_params", "openai.types.beta.thread_create_and_run_params", "openai.types.beta.thread", "openai.types.shared.chat_model", "openai.types.beta.thread_deleted", "openai.types.shared_params.metadata", "openai.types.beta.assistant_tool_param", "openai.types.beta.assistant_stream_event", "openai.types.beta.assistant_tool_choice_option_param", "openai.types.beta.assistant_response_format_option_param", "openai.types.beta", "openai.lib.streaming", "openai._legacy_response", "openai._types", "openai._utils", "openai._compat", "openai._resource", "openai._response", "openai._streaming", "openai._base_client", "__future__", "typing_extensions", "typing", "functools", "httpx", "openai", "builtins", "_frozen_importlib", "abc", "httpx._config", "openai._models", "openai._utils._utils", "openai.lib", "openai.lib.streaming._assistants", "openai.resources.beta.threads.runs", "openai.types", "openai.types.beta.assistant_tool_choice_function_param", "openai.types.beta.assistant_tool_choice_param", "openai.types.beta.code_interpreter_tool_param", "openai.types.beta.file_search_tool_param", "openai.types.beta.function_tool_param", "openai.types.beta.threads", "openai.types.beta.threads.image_file_content_block_param", "openai.types.beta.threads.image_file_param", "openai.types.beta.threads.image_url_content_block_param", "openai.types.beta.threads.image_url_param", "openai.types.beta.threads.text_content_block_param", "openai.types.shared_params", "openai.types.shared_params.function_definition", "openai.types.shared_params.response_format_json_object", "openai.types.shared_params.response_format_json_schema", "openai.types.shared_params.response_format_text", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main"], "hash": "11894453239a08a1000e1a507030dae0e5b5947b", "id": "openai.resources.beta.threads.threads", "ignore_all": true, "interface_hash": "95aee530b97b2e3dc82c77652dacdc6f303859fb", "mtime": 1754241436, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\threads\\threads.py", "plugin_data": null, "size": 99762, "suppressed": [], "version_id": "1.17.1"}