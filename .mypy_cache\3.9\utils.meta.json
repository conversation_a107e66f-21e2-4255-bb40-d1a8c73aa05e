{"data_mtime": 1754409297, "dep_lines": [7, 8, 9, 10, 11, 12, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["os", "sys", "subprocess", "<PERSON><PERSON><PERSON>", "shutil", "pathlib", "builtins", "_frozen_importlib", "_hashlib", "_io", "_typeshed", "abc", "io", "typing", "typing_extensions"], "hash": "7bb283f08c94ada4c82eacfa98d0e9c7284e79b9", "id": "utils", "ignore_all": false, "interface_hash": "68310ff5015a4415bcac2065b30bebcc4add7ce0", "mtime": 1754250952, "options": {"allow_redefinition": false, "allow_redefinition_new": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": true, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\scripts\\utils.py", "plugin_data": null, "size": 10221, "suppressed": [], "version_id": "1.17.1"}