{".class": "MypyFile", "_fullname": "openai.resources.beta.threads", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncMessages": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.AsyncMessages", "kind": "Gdef"}, "AsyncMessagesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.AsyncMessagesWithRawResponse", "kind": "Gdef"}, "AsyncMessagesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.AsyncMessagesWithStreamingResponse", "kind": "Gdef"}, "AsyncRuns": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.AsyncRuns", "kind": "Gdef"}, "AsyncRunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.AsyncRunsWithRawResponse", "kind": "Gdef"}, "AsyncRunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.AsyncRunsWithStreamingResponse", "kind": "Gdef"}, "AsyncThreads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.AsyncThreads", "kind": "Gdef"}, "AsyncThreadsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.AsyncThreadsWithRawResponse", "kind": "Gdef"}, "AsyncThreadsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.AsyncThreadsWithStreamingResponse", "kind": "Gdef"}, "Messages": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.Messages", "kind": "Gdef"}, "MessagesWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.MessagesWithRawResponse", "kind": "Gdef"}, "MessagesWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.messages.MessagesWithStreamingResponse", "kind": "Gdef"}, "Runs": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.Runs", "kind": "Gdef"}, "RunsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.RunsWithRawResponse", "kind": "Gdef"}, "RunsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.runs.runs.RunsWithStreamingResponse", "kind": "Gdef"}, "Threads": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.Threads", "kind": "Gdef"}, "ThreadsWithRawResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.ThreadsWithRawResponse", "kind": "Gdef"}, "ThreadsWithStreamingResponse": {".class": "SymbolTableNode", "cross_ref": "openai.resources.beta.threads.threads.ThreadsWithStreamingResponse", "kind": "Gdef"}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.resources.beta.threads.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.resources.beta.threads.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\resources\\beta\\threads\\__init__.py"}