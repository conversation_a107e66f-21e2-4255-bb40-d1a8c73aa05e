{".class": "MypyFile", "_fullname": "openai.types.evals", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CreateEvalCompletionsRunDataSource": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.create_eval_completions_run_data_source.CreateEvalCompletionsRunDataSource", "kind": "Gdef"}, "CreateEvalCompletionsRunDataSourceParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.create_eval_completions_run_data_source_param.CreateEvalCompletionsRunDataSourceParam", "kind": "Gdef"}, "CreateEvalJSONLRunDataSource": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.create_eval_jsonl_run_data_source.CreateEvalJSONLRunDataSource", "kind": "Gdef"}, "CreateEvalJSONLRunDataSourceParam": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.create_eval_jsonl_run_data_source_param.CreateEvalJSONLRunDataSourceParam", "kind": "Gdef"}, "EvalAPIError": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.eval_api_error.EvalAPIError", "kind": "Gdef"}, "RunCancelResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.run_cancel_response.RunCancelResponse", "kind": "Gdef"}, "RunCreateParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.run_create_params.RunCreateParams", "kind": "Gdef"}, "RunCreateResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.run_create_response.RunCreateResponse", "kind": "Gdef"}, "RunDeleteResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.run_delete_response.RunDeleteResponse", "kind": "Gdef"}, "RunListParams": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.run_list_params.RunListParams", "kind": "Gdef"}, "RunListResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.run_list_response.RunListResponse", "kind": "Gdef"}, "RunRetrieveResponse": {".class": "SymbolTableNode", "cross_ref": "openai.types.evals.run_retrieve_response.RunRetrieveResponse", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.evals.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.evals.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.evals.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.evals.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.evals.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__path__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.evals.__path__", "name": "__path__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.evals.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\evals\\__init__.py"}