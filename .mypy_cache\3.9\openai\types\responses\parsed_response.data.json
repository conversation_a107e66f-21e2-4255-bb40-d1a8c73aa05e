{".class": "MypyFile", "_fullname": "openai.types.responses.parsed_response", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Annotated": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.Annotated", "kind": "Gdef", "module_public": false}, "ContentType": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "name": "ContentType", "upper_bound": "builtins.object", "values": [], "variance": 0}}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef", "module_public": false}, "GenericModel": {".class": "SymbolTableNode", "cross_ref": "openai._models.GenericModel", "kind": "Gdef", "module_public": false}, "ImageGenerationCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item.ImageGenerationCall", "kind": "Gdef", "module_public": false}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef", "module_public": false}, "LocalShellCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item.LocalShellCall", "kind": "Gdef", "module_public": false}, "LocalShellCallAction": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item.LocalShellCallAction", "kind": "Gdef", "module_public": false}, "McpApprovalRequest": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item.McpApprovalRequest", "kind": "Gdef", "module_public": false}, "McpCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item.McpCall", "kind": "Gdef", "module_public": false}, "McpListTools": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_item.McpListTools", "kind": "Gdef", "module_public": false}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef", "module_public": false}, "ParsedContent": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedContent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "openai.types.responses.parsed_response.ParsedContent", "line": 42, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedContent", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponseOutputText"}, "openai.types.responses.response_output_refusal.ResponseOutputRefusal"], "uses_pep604_syntax": false}}}, "ParsedResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.types.responses.response.Response", "openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.responses.parsed_response.ParsedResponse", "name": "ParsedResponse", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.responses.parsed_response.ParsedResponse", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 35, "name": "id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 38, "name": "created_at", "type": "builtins.float"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 41, "name": "error", "type": {".class": "UnionType", "items": ["openai.types.responses.response_error.ResponseError", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 44, "name": "incomplete_details", "type": {".class": "UnionType", "items": ["openai.types.responses.response.IncompleteDetails", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 47, "name": "instructions", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_input_item.ResponseInputItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 55, "name": "metadata", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.metadata.Metadata"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 65, "name": "model", "type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.responses_model.ResponsesModel"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 74, "name": "object", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "response"}}, {"alias": null, "column": 8, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 83, "name": "output", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.types.responses.parsed_response.ParsedResponseOutputItem"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 87, "name": "parallel_tool_calls", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 90, "name": "temperature", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 98, "name": "tool_choice", "type": {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response.ToolChoice"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 105, "name": "tools", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool.Tool"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 123, "name": "top_p", "type": {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 132, "name": "background", "type": {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 138, "name": "max_output_tokens", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 145, "name": "max_tool_calls", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 153, "name": "previous_response_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 160, "name": "prompt", "type": {".class": "UnionType", "items": ["openai.types.responses.response_prompt.ResponsePrompt", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 166, "name": "prompt_cache_key", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 173, "name": "reasoning", "type": {".class": "UnionType", "items": ["openai.types.shared.reasoning.Reasoning", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 180, "name": "safety_identifier", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 189, "name": "service_tier", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "flex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scale"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "priority"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 209, "name": "status", "type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_status.ResponseStatus"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 216, "name": "text", "type": {".class": "UnionType", "items": ["openai.types.responses.response_text_config.ResponseTextConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 225, "name": "top_logprobs", "type": {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 231, "name": "truncation", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disabled"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 241, "name": "usage", "type": {".class": "UnionType", "items": ["openai.types.responses.response_usage.ResponseUsage", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 247, "name": "user", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.responses.parsed_response", "mro": ["openai.types.responses.parsed_response.ParsedResponse", "openai.types.responses.response.Response", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.responses.parsed_response.ParsedResponse.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 5, 5, 5, 5, 3, 3, 3, 3, 5, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "_request_id", "id", "created_at", "error", "incomplete_details", "instructions", "metadata", "model", "object", "output", "parallel_tool_calls", "temperature", "tool_choice", "tools", "top_p", "background", "max_output_tokens", "max_tool_calls", "previous_response_id", "prompt", "prompt_cache_key", "reasoning", "safety_identifier", "service_tier", "status", "text", "top_logprobs", "truncation", "usage", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.responses.parsed_response.ParsedResponse.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 5, 5, 5, 5, 3, 3, 3, 3, 5, 3, 3, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["self", "_request_id", "id", "created_at", "error", "incomplete_details", "instructions", "metadata", "model", "object", "output", "parallel_tool_calls", "temperature", "tool_choice", "tools", "top_p", "background", "max_output_tokens", "max_tool_calls", "previous_response_id", "prompt", "prompt_cache_key", "reasoning", "safety_identifier", "service_tier", "status", "text", "top_logprobs", "truncation", "usage", "user"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.float", {".class": "UnionType", "items": ["openai.types.responses.response_error.ResponseError", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response.IncompleteDetails", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_input_item.ResponseInputItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.metadata.Metadata"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.responses_model.ResponsesModel"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "response"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.types.responses.parsed_response.ParsedResponseOutputItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response.ToolChoice"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool.Tool"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response_prompt.ResponsePrompt", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.shared.reasoning.Reasoning", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "flex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scale"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "priority"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "queued"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response_text_config.ResponseTextConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disabled"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response_usage.ResponseUsage", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ParsedResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "created_at", "error", "incomplete_details", "instructions", "metadata", "model", "object", "output", "parallel_tool_calls", "temperature", "tool_choice", "tools", "top_p", "background", "max_output_tokens", "max_tool_calls", "previous_response_id", "prompt", "prompt_cache_key", "reasoning", "safety_identifier", "service_tier", "status", "text", "top_logprobs", "truncation", "usage", "user"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.responses.parsed_response.ParsedResponse.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "created_at", "error", "incomplete_details", "instructions", "metadata", "model", "object", "output", "parallel_tool_calls", "temperature", "tool_choice", "tools", "top_p", "background", "max_output_tokens", "max_tool_calls", "previous_response_id", "prompt", "prompt_cache_key", "reasoning", "safety_identifier", "service_tier", "status", "text", "top_logprobs", "truncation", "usage", "user"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.float", {".class": "UnionType", "items": ["openai.types.responses.response_error.ResponseError", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response.IncompleteDetails", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_input_item.ResponseInputItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.metadata.Metadata"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.responses_model.ResponsesModel"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "response"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.types.responses.parsed_response.ParsedResponseOutputItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response.ToolChoice"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool.Tool"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response_prompt.ResponsePrompt", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.shared.reasoning.Reasoning", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "flex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scale"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "priority"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "queued"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response_text_config.ResponseTextConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disabled"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response_usage.ResponseUsage", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ParsedResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.responses.parsed_response.ParsedResponse.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "created_at", "error", "incomplete_details", "instructions", "metadata", "model", "object", "output", "parallel_tool_calls", "temperature", "tool_choice", "tools", "top_p", "background", "max_output_tokens", "max_tool_calls", "previous_response_id", "prompt", "prompt_cache_key", "reasoning", "safety_identifier", "service_tier", "status", "text", "top_logprobs", "truncation", "usage", "user"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.float", {".class": "UnionType", "items": ["openai.types.responses.response_error.ResponseError", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response.IncompleteDetails", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_input_item.ResponseInputItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.metadata.Metadata"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.shared.responses_model.ResponsesModel"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "response"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.types.responses.parsed_response.ParsedResponseOutputItem"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool", {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response.ToolChoice"}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.tool.Tool"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.float", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.bool", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response_prompt.ResponsePrompt", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.shared.reasoning.Reasoning", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "default"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "flex"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "scale"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "priority"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "failed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "cancelled"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "queued"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response_text_config.ResponseTextConfig", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.int", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "auto"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "disabled"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["openai.types.responses.response_usage.ResponseUsage", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ParsedResponse", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "output": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.responses.parsed_response.ParsedResponse.output", "name": "output", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.types.responses.parsed_response.ParsedResponseOutputItem"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "output_parsed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated", "is_trivial_self"], "fullname": "openai.types.responses.parsed_response.ParsedResponse.output_parsed", "name": "output_parsed", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "output_parsed of ParsedResponse", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "openai.types.responses.parsed_response.ParsedResponse.output_parsed", "name": "output_parsed", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "output_parsed of ParsedResponse", "ret_type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ParsedResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponse", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponse"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ContentType"], "typeddict_type": null}}, "ParsedResponseFunctionToolCall": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.types.responses.response_function_tool_call.ResponseFunctionToolCall"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall", "name": "ParsedResponseFunctionToolCall", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 12, "name": "arguments", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 15, "name": "call_id", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 18, "name": "name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 21, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "function_call"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 24, "name": "id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 27, "name": "status", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 56, "name": "parsed_arguments", "type": "builtins.object"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.responses.parsed_response", "mro": ["openai.types.responses.parsed_response.ParsedResponseFunctionToolCall", "openai.types.responses.response_function_tool_call.ResponseFunctionToolCall", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__api_exclude__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall.__api_exclude__", "name": "__api_exclude__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["self", "_request_id", "arguments", "call_id", "name", "type", "id", "status", "parsed_arguments"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 5, 5, 5], "arg_names": ["self", "_request_id", "arguments", "call_id", "name", "type", "id", "status", "parsed_arguments"], "arg_types": ["openai.types.responses.parsed_response.ParsedResponseFunctionToolCall", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "function_call"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ParsedResponseFunctionToolCall", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "arguments", "call_id", "name", "type", "id", "status", "parsed_arguments"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "arguments", "call_id", "name", "type", "id", "status", "parsed_arguments"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "function_call"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ParsedResponseFunctionToolCall", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "arguments", "call_id", "name", "type", "id", "status", "parsed_arguments"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", "builtins.str", "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "function_call"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.object"], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ParsedResponseFunctionToolCall", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "parsed_arguments": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall.parsed_arguments", "name": "parsed_arguments", "setter_type": null, "type": "builtins.object"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ParsedResponseOutputItem": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "TypeAlias", "alias_tvars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputItem", "upper_bound": "builtins.object", "values": [], "variance": 0}], "column": 0, "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputItem", "line": 61, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "UnionType", "items": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputItem", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponseOutputMessage"}, "openai.types.responses.parsed_response.ParsedResponseFunctionToolCall", "openai.types.responses.response_file_search_tool_call.ResponseFileSearchToolCall", "openai.types.responses.response_function_web_search.ResponseFunctionWebSearch", "openai.types.responses.response_computer_tool_call.ResponseComputerToolCall", "openai.types.responses.response_reasoning_item.ResponseReasoningItem", "openai.types.responses.response_output_item.McpCall", "openai.types.responses.response_output_item.McpApprovalRequest", "openai.types.responses.response_output_item.ImageGenerationCall", "openai.types.responses.response_output_item.LocalShellCall", "openai.types.responses.response_output_item.LocalShellCallAction", "openai.types.responses.response_output_item.McpListTools", "openai.types.responses.response_code_interpreter_tool_call.ResponseCodeInterpreterToolCall"], "uses_pep604_syntax": false}}}, "ParsedResponseOutputMessage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.types.responses.response_output_message.ResponseOutputMessage", "openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "name": "ParsedResponseOutputMessage", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 17, "name": "id", "type": "builtins.str"}, {"alias": null, "column": 8, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 50, "name": "content", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.types.responses.parsed_response.ParsedContent"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 23, "name": "role", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "assistant"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 26, "name": "status", "type": {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 33, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "message"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.responses.parsed_response", "mro": ["openai.types.responses.parsed_response.ParsedResponseOutputMessage", "openai.types.responses.response_output_message.ResponseOutputMessage", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputMessage.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "id", "content", "role", "status", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputMessage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 3, 3], "arg_names": ["self", "_request_id", "id", "content", "role", "status", "type"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponseOutputMessage"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.types.responses.parsed_response.ParsedContent"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "assistant"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "message"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ParsedResponseOutputMessage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "content", "role", "status", "type"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputMessage.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "content", "role", "status", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.types.responses.parsed_response.ParsedContent"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "assistant"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "message"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ParsedResponseOutputMessage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputMessage.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "id", "content", "role", "status", "type"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "builtins.str", {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.types.responses.parsed_response.ParsedContent"}], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "assistant"}, {".class": "UnionType", "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "in_progress"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "completed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "incomplete"}], "uses_pep604_syntax": false}, {".class": "LiteralType", "fallback": "builtins.str", "value": "message"}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ParsedResponseOutputMessage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputMessage.content", "name": "content", "setter_type": null, "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "upper_bound": "builtins.object", "values": [], "variance": 0}], "type_ref": "openai.types.responses.parsed_response.ParsedContent"}], "extra_attrs": null, "type_ref": "builtins.list"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputMessage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputMessage", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponseOutputMessage"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ContentType"], "typeddict_type": null}}, "ParsedResponseOutputText": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["openai.types.responses.response_output_text.ResponseOutputText", "openai._models.BaseModel"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputText", "name": "ParsedResponseOutputText", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputText", "upper_bound": "builtins.object", "values": [], "variance": 0}]}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputText", "has_param_spec_type": false, "metaclass_type": "pydantic._internal._model_construction.ModelMetaclass", "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 214, "name": "__pydantic_extra__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 217, "name": "__pydantic_fields_set__", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": false, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": true, "kw_only": true, "line": 220, "name": "__pydantic_private__", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}, {"alias": null, "column": 8, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "_request_id", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 108, "name": "annotations", "type": {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_output_text.Annotation"}], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 111, "name": "text", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 114, "name": "type", "type": {".class": "LiteralType", "fallback": "builtins.str", "value": "output_text"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 117, "name": "logprobs", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["openai.types.responses.response_output_text.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": true, "line": 39, "name": "parsed", "type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputText", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "openai.types.responses.parsed_response", "mro": ["openai.types.responses.parsed_response.ParsedResponseOutputText", "openai.types.responses.response_output_text.ResponseOutputText", "openai._models.BaseModel", "pydantic.main.BaseModel", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputText.__dataclass_fields__", "name": "__dataclass_fields__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 3, 3, 3, 5, 5], "arg_names": ["self", "_request_id", "annotations", "text", "type", "logprobs", "parsed"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputText.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 5, 3, 3, 3, 5, 5], "arg_names": ["self", "_request_id", "annotations", "text", "type", "logprobs", "parsed"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputText", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponseOutputText"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_output_text.Annotation"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_text"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["openai.types.responses.response_output_text.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputText", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__init__ of ParsedResponseOutputText", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "annotations", "text", "type", "logprobs", "parsed"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputText.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "annotations", "text", "type", "logprobs", "parsed"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_output_text.Annotation"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_text"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["openai.types.responses.response_output_text.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputText", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ParsedResponseOutputText", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputText.__mypy-replace", "name": "__mypy-replace", "setter_type": null, "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["__pydantic_extra__", "__pydantic_fields_set__", "__pydantic_private__", "_request_id", "annotations", "text", "type", "logprobs", "parsed"], "arg_types": [{".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "Instance", "args": [{".class": "TypeAliasType", "args": [], "type_ref": "openai.types.responses.response_output_text.Annotation"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "LiteralType", "fallback": "builtins.str", "value": "output_text"}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["openai.types.responses.response_output_text.Logprob"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputText", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_bound": false, "is_ellipsis_args": false, "name": "__mypy-replace of ParsedResponseOutputText", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "parsed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputText.parsed", "name": "parsed", "setter_type": null, "type": {".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputText", "upper_bound": "builtins.object", "values": [], "variance": 0}, {".class": "NoneType"}], "uses_pep604_syntax": false}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ParsedResponseOutputText.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "openai.types.responses.parsed_response.ContentType", "id": 1, "name": "ContentType", "namespace": "openai.types.responses.parsed_response.ParsedResponseOutputText", "upper_bound": "builtins.object", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "openai.types.responses.parsed_response.ParsedResponseOutputText"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["ContentType"], "typeddict_type": null}}, "PropertyInfo": {".class": "SymbolTableNode", "cross_ref": "openai._utils._transform.PropertyInfo", "kind": "Gdef", "module_public": false}, "Response": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response.Response", "kind": "Gdef", "module_public": false}, "ResponseCodeInterpreterToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_code_interpreter_tool_call.ResponseCodeInterpreterToolCall", "kind": "Gdef", "module_public": false}, "ResponseComputerToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_computer_tool_call.ResponseComputerToolCall", "kind": "Gdef", "module_public": false}, "ResponseFileSearchToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_file_search_tool_call.ResponseFileSearchToolCall", "kind": "Gdef", "module_public": false}, "ResponseFunctionToolCall": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_tool_call.ResponseFunctionToolCall", "kind": "Gdef", "module_public": false}, "ResponseFunctionWebSearch": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_function_web_search.ResponseFunctionWebSearch", "kind": "Gdef", "module_public": false}, "ResponseOutputMessage": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_message.ResponseOutputMessage", "kind": "Gdef", "module_public": false}, "ResponseOutputRefusal": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_refusal.ResponseOutputRefusal", "kind": "Gdef", "module_public": false}, "ResponseOutputText": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_output_text.ResponseOutputText", "kind": "Gdef", "module_public": false}, "ResponseReasoningItem": {".class": "SymbolTableNode", "cross_ref": "openai.types.responses.response_reasoning_item.ResponseReasoningItem", "kind": "Gdef", "module_public": false}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef", "module_public": false}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeAlias", "kind": "Gdef", "module_public": false}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing.TypeVar", "kind": "Gdef", "module_public": false}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef", "module_public": false}, "__all__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "openai.types.responses.parsed_response.__all__", "name": "__all__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.parsed_response.__annotations__", "name": "__annotations__", "setter_type": null, "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.parsed_response.__doc__", "name": "__doc__", "setter_type": null, "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.parsed_response.__file__", "name": "__file__", "setter_type": null, "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.parsed_response.__name__", "name": "__name__", "setter_type": null, "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.parsed_response.__package__", "name": "__package__", "setter_type": null, "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "module_public": false, "node": {".class": "Var", "flags": ["is_ready"], "fullname": "openai.types.responses.parsed_response.__spec__", "name": "__spec__", "setter_type": null, "type": "_frozen_importlib.ModuleSpec"}}}, "path": "c:\\Users\\<USER>\\Desktop\\Mes_projets\\WTF\\Windows_and_Linux\\myvenv\\Lib\\site-packages\\openai\\types\\responses\\parsed_response.py"}